package net.devgrip.server.util;

import net.devgrip.commons.utils.StringUtils;
import org.junit.Test;

import java.util.NoSuchElementException;

import static com.google.mu.util.Substring.before;
import static com.google.mu.util.Substring.last;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;

/**
 *
 */

public class ParsedEmailAddressTest {

    @Test
    public void testParse() {
        ParsedEmailAddress parse = ParsedEmailAddress.parse("<EMAIL>");
        assertEquals("xxx", parse.getName());
        assertEquals("google.com", parse.getDomain());
        assertThrows(IllegalArgumentException.class, () -> ParsedEmailAddress.parse("xxxgoogle.com"));
    }
    @Test
    public void testGetOriginalAddress() {
        ParsedEmailAddress xxplusWork = new ParsedEmailAddress("xx+work", "gmail.com");
        assertEquals("<EMAIL>", xxplusWork.getOriginalAddress());
        ParsedEmailAddress xx = new ParsedEmailAddress("xx", "gmail.com");
        assertEquals("<EMAIL>", xx.getOriginalAddress());

    }
    @Test
    public void testGetServerAddress() {
        String serverAddress = "127.0.0.1:80";
        assertEquals("127.0.0.1", StringUtils.substringBefore(serverAddress, ":"));
        assertEquals("127.0.0.1", before(last(":")).from(serverAddress).orElseThrow(() -> new NoSuchElementException("no host found")));
        String fake1 = "127.0.0.180:";
        assertEquals("127.0.0.180", StringUtils.substringBefore(fake1, ":"));
        assertEquals("127.0.0.180", before(last(":")).from(fake1).orElse(fake1));

        String fake = "127.0.0.180";
        assertEquals("127.0.0.180", StringUtils.substringBefore(fake, ":"));
        assertEquals("127.0.0.180", before(last(":")).from(fake).orElse(fake));
    }
}
