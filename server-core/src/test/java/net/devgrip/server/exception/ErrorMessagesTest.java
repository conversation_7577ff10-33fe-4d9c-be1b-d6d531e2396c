package net.devgrip.server.exception;

import org.junit.Assert;
import org.junit.Test;

import java.util.List;
import java.util.Locale;

/**
 *
 */
public class ErrorMessagesTest {

    @Test
    public void testGetMessage() {
        set_locale_as_en();
        Assert.assertEquals("Unable to find issue in project (issue number: #1, project: p2)", ErrorMessages.UNABLE_TO_FIND_ISSUE_IN_PROJECT.getMessage("1", "p2"));
        Assert.assertEquals("Project not found or inaccessible: p1", ErrorMessages.PROJECT_NOT_FOUND_OR_INACCESSIBLE.getMessage("p1"));
        Assert.assertEquals("Unable to find issue in project (issue number: #{0}, project: {1})", ErrorMessages.UNABLE_TO_FIND_ISSUE_IN_PROJECT.getMessage());
        Assert.assertEquals("Missing required parameter: build number", ErrorMessages.MISSING_REQUIRED_PARAM.getMessage("build number"));
        Assert.assertEquals("Missing required field: [f1,f2,f3]", ErrorMessages.MISSING_REQUIRED_FIELD.getMessage(List.of("f1,f2,f3")));
        Assert.assertEquals("Invalid choice values: [f1,f2,f3]", ErrorMessages.INVALID_CHOICE_VALUES.getMessage(List.of("f1,f2,f3")));


        set_locale_zh_CN();
        Assert.assertEquals("找不到问题(问题编号:#1，项目:p2)", ErrorMessages.UNABLE_TO_FIND_ISSUE_IN_PROJECT.getMessage("1", "p2"));
        Assert.assertEquals("项目不存在或无权访问:p1", ErrorMessages.PROJECT_NOT_FOUND_OR_INACCESSIBLE.getMessage("p1"));

        set_locale_as_en();
        Assert.assertNotEquals("fake content", ErrorMessages.UNABLE_TO_FIND_ISSUE_IN_PROJECT.getMessage("fake", "fake"));
    }

    private void set_locale_as_en() {
        Locale.setDefault(Locale.ENGLISH);
    }
    private void set_locale_zh_CN() {
        Locale.setDefault(Locale.SIMPLIFIED_CHINESE);
    }
}
