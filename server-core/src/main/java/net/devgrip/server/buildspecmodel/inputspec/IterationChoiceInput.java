package net.devgrip.server.buildspecmodel.inputspec;

import edu.emory.mathcs.backport.java.util.Collections;
import net.devgrip.server.buildspecmodel.inputspec.choiceinput.ChoiceInput;
import net.devgrip.server.model.Iteration;
import net.devgrip.server.model.Project;

import javax.validation.ValidationException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static net.devgrip.server.exception.ErrorMessages.DOES_NOT_SUPPORT_MULTIPLE_VALUES;

public class IterationChoiceInput {
	
	public static String getPropertyDef(InputSpec inputSpec, Map<String, Integer> indexes) {
		int index = indexes.get(inputSpec.getName());
		StringBuffer buffer = new StringBuffer();
		inputSpec.appendField(buffer, index, inputSpec.isAllowMultiple()? "List<String>": "String");
		inputSpec.appendCommonAnnotations(buffer, index);
		if (!inputSpec.isAllowEmpty()) {
			if (inputSpec.isAllowMultiple())
				buffer.append("    @Size(min=1, message=\"{AtLeastOneIterationNeedsToBeSelected}\")\n");
			else
				buffer.append("    @NotEmpty\n");
		}
		
		buffer.append("    @IterationChoice\n");
		
		if (inputSpec.isAllowMultiple())
			inputSpec.appendMethods(buffer, index, "List<String>", null, null);
		else 
			inputSpec.appendMethods(buffer, index, "String", null, null);
		
		return buffer.toString();
	}

	public static Object convertToObject(InputSpec inputSpec, List<String> strings) {
		if (inputSpec.isAllowMultiple()) 
			return strings;
		else if (strings.isEmpty())
			return null;
		else if (strings.size() == 1)
			return strings.iterator().next();
		else 
			throw new ValidationException(DOES_NOT_SUPPORT_MULTIPLE_VALUES.getMessage());
	}

	@SuppressWarnings("unchecked")
	public static List<String> convertToStrings(InputSpec inputSpec, Object value) {
        return ChoiceInput.convertToStrings(inputSpec, value);
	}

	public static int getOrdinal(String fieldValue) {
		int ordinal = -1;
		Project project = Project.get();
		if (project != null) {
			List<Iteration> iterations = new ArrayList<>(project.getHierarchyIterations());
			Collections.sort(iterations);
			for (Iteration iteration: iterations) {
				ordinal++;
				if (iteration.getName().equals(fieldValue))
					break;
			}
		} 
		return ordinal;
	}
	
}
