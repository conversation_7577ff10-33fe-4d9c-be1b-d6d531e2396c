package net.devgrip.server.buildspecmodel.inputspec.groupchoiceinput;

import com.google.common.collect.Lists;
import net.devgrip.server.AppServer;
import net.devgrip.server.buildspecmodel.inputspec.InputSpec;
import net.devgrip.server.buildspecmodel.inputspec.groupchoiceinput.choiceprovider.ChoiceProvider;
import net.devgrip.server.buildspecmodel.inputspec.groupchoiceinput.defaultvalueprovider.DefaultValueProvider;
import net.devgrip.server.model.Group;

import javax.validation.ValidationException;
import javax.validation.Validator;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static net.devgrip.server.exception.ErrorMessages.DOES_NOT_SUPPORT_MULTIPLE_VALUES;

public class GroupChoiceInput {
	
	public static List<String> getPossibleValues(ChoiceProvider choiceProvider) {
		List<String> possibleValues = new ArrayList<>();
		if (AppServer.getInstance(Validator.class).validate(choiceProvider).isEmpty()) {
			for (Group group: choiceProvider.getChoices(true))
				possibleValues.add(group.getName());
		}
		return possibleValues;
	}

	public static String getPropertyDef(InputSpec inputSpec, Map<String, Integer> indexes, 
			ChoiceProvider choiceProvider, DefaultValueProvider defaultValueProvider) {
		int index = indexes.get(inputSpec.getName());
		StringBuffer buffer = new StringBuffer();
		inputSpec.appendField(buffer, index, "String");
		inputSpec.appendCommonAnnotations(buffer, index);
		if (!inputSpec.isAllowEmpty())
			buffer.append("    @NotEmpty\n");
		inputSpec.appendChoiceProvider(buffer, index, "@GroupChoice");
		inputSpec.appendMethods(buffer, index, "String", choiceProvider, defaultValueProvider);
		
		return buffer.toString();
	}

	public static Object convertToObject(List<String> strings) {
		if (strings.isEmpty())
			return null;
		else if (strings.size() == 1) 
			return strings.iterator().next();
		else 
			throw new ValidationException(DOES_NOT_SUPPORT_MULTIPLE_VALUES.getMessage());
	}

	public static List<String> convertToStrings(Object value) {
		if (value instanceof String)
			return Lists.newArrayList((String) value);
		else
			return new ArrayList<>();
	}

}
