package net.devgrip.server.buildspecmodel.inputspec;

import com.google.common.collect.Lists;

import javax.validation.ValidationException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static net.devgrip.server.exception.ErrorMessages.DOES_NOT_SUPPORT_MULTIPLE_VALUES;
import static net.devgrip.server.exception.ErrorMessages.INVALID_ISSUE_NUMBER;

public class IssueChoiceInput {
	
	public static String getPropertyDef(InputSpec inputSpec, Map<String, Integer> indexes) {
		int index = indexes.get(inputSpec.getName());
		StringBuffer buffer = new StringBuffer();
		inputSpec.appendField(buffer, index, "Long");
		inputSpec.appendCommonAnnotations(buffer, index);
		if (!inputSpec.isAllowEmpty())
			buffer.append("    @NotNull\n");
		buffer.append("    @IssueChoice\n");
		inputSpec.appendMethods(buffer, index, "Long", null, null);
		
		return buffer.toString();
	}

	public static Object convertToObject(List<String> strings) {
		if (strings.isEmpty()) {
			return null;
		} else if (strings.size() == 1) {
			String value = strings.iterator().next();
			try {
				return Long.valueOf(value);
			} catch (NumberFormatException e) {
				throw new ValidationException(INVALID_ISSUE_NUMBER.getMessage(value));
			}
		} else {
			throw new ValidationException(DOES_NOT_SUPPORT_MULTIPLE_VALUES.getMessage());
		}
	}

	public static List<String> convertToStrings(Object value) {
		if (value instanceof Long)
			return Lists.newArrayList(value.toString());
		else
			return new ArrayList<>();
	}

}
