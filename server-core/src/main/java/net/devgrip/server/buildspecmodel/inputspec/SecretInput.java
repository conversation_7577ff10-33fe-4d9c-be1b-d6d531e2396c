package net.devgrip.server.buildspecmodel.inputspec;

import com.google.common.collect.Lists;

import javax.validation.ValidationException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static net.devgrip.server.exception.ErrorMessages.DOES_NOT_SUPPORT_MULTIPLE_VALUES;

public class SecretInput {

	public static final String MASK = "*****";
	
	public static final String LITERAL_VALUE_PREFIX = "$OneDev-Secret-Literal$";
	
	public static String getPropertyDef(InputSpec inputSpec, Map<String, Integer> indexes) {
		int index = indexes.get(inputSpec.getName());
		StringBuffer buffer = new StringBuffer();
		inputSpec.appendField(buffer, index, "String");
		inputSpec.appendCommonAnnotations(buffer, index);
		buffer.append("    @NotEmpty\n");
		buffer.append("    @Password\n");
		inputSpec.appendMethods(buffer, index, "String", null, null);
		
		return buffer.toString();
	}

	public static Object convertToObject(List<String> strings) {
		if (strings.isEmpty())
			return null;
		else if (strings.size() == 1)
			return strings.iterator().next();
		else
			throw new ValidationException(DOES_NOT_SUPPORT_MULTIPLE_VALUES.getMessage());
	}

	public static List<String> convertToStrings(Object value) {
		if (value instanceof String)
			return Lists.newArrayList((String)value);
		else
			return new ArrayList<>();
	}

}
