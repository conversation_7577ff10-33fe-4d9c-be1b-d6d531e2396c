package net.devgrip.server.modelcontextprotocol;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Splitter;
import net.devgrip.commons.utils.StringUtils;
import net.devgrip.server.SubscriptionManager;
import net.devgrip.server.entitymanager.IssueChangeManager;
import net.devgrip.server.entitymanager.IssueCommentManager;
import net.devgrip.server.entitymanager.IssueLinkManager;
import net.devgrip.server.entitymanager.IssueManager;
import net.devgrip.server.entitymanager.IssueWorkManager;
import net.devgrip.server.entitymanager.IterationManager;
import net.devgrip.server.entitymanager.LabelSpecManager;
import net.devgrip.server.entitymanager.LinkSpecManager;
import net.devgrip.server.entitymanager.ProjectManager;
import net.devgrip.server.entitymanager.PullRequestAssignmentManager;
import net.devgrip.server.entitymanager.PullRequestChangeManager;
import net.devgrip.server.entitymanager.PullRequestCommentManager;
import net.devgrip.server.entitymanager.PullRequestLabelManager;
import net.devgrip.server.entitymanager.PullRequestManager;
import net.devgrip.server.entitymanager.PullRequestReviewManager;
import net.devgrip.server.entitymanager.SettingManager;
import net.devgrip.server.entitymanager.UserManager;
import net.devgrip.server.entityreference.IssueReference;
import net.devgrip.server.entityreference.PullRequestReference;
import net.devgrip.server.exception.InvalidIssueLinkException;
import net.devgrip.server.exception.RequiredFieldMissingException;
import net.devgrip.server.exception.ReviewRejectException;
import net.devgrip.server.git.service.GitService;
import net.devgrip.server.model.Issue;
import net.devgrip.server.model.IssueComment;
import net.devgrip.server.model.IssueLink;
import net.devgrip.server.model.IssueSchedule;
import net.devgrip.server.model.IssueWork;
import net.devgrip.server.model.Iteration;
import net.devgrip.server.model.LabelSpec;
import net.devgrip.server.model.Project;
import net.devgrip.server.model.PullRequest;
import net.devgrip.server.model.PullRequestAssignment;
import net.devgrip.server.model.PullRequestComment;
import net.devgrip.server.model.PullRequestReview;
import net.devgrip.server.model.PullRequestUpdate;
import net.devgrip.server.model.User;
import net.devgrip.server.model.support.issue.field.FieldUtils;
import net.devgrip.server.model.support.issue.field.spec.BooleanField;
import net.devgrip.server.model.support.issue.field.spec.DateField;
import net.devgrip.server.model.support.issue.field.spec.DateTimeField;
import net.devgrip.server.model.support.issue.field.spec.FieldSpec;
import net.devgrip.server.model.support.issue.field.spec.FloatField;
import net.devgrip.server.model.support.issue.field.spec.GroupChoiceField;
import net.devgrip.server.model.support.issue.field.spec.IntegerField;
import net.devgrip.server.model.support.issue.field.spec.choicefield.ChoiceField;
import net.devgrip.server.model.support.issue.field.spec.userchoicefield.UserChoiceField;
import net.devgrip.server.model.support.issue.transitionspec.ManualSpec;
import net.devgrip.server.model.support.pullrequest.AutoMerge;
import net.devgrip.server.model.support.pullrequest.MergeStrategy;
import net.devgrip.server.model.support.pullrequest.changedata.PullRequestApproveData;
import net.devgrip.server.model.support.pullrequest.changedata.PullRequestRequestedForChangesData;
import net.devgrip.server.rest.InvalidParamException;
import net.devgrip.server.rest.annotation.Api;
import net.devgrip.server.rest.resource.support.RestConstants;
import net.devgrip.server.search.entity.EntityQuery;
import net.devgrip.server.search.entity.issue.IssueQuery;
import net.devgrip.server.search.entity.issue.IssueQueryParseOption;
import net.devgrip.server.search.entity.pullrequest.PullRequestQuery;
import net.devgrip.server.security.SecurityUtils;
import net.devgrip.server.util.DateUtils;
import net.devgrip.server.util.ProjectAndBranch;
import net.devgrip.server.util.ProjectScope;
import net.devgrip.server.web.UrlManager;
import org.apache.shiro.authz.UnauthenticatedException;
import org.apache.shiro.authz.UnauthorizedException;
import org.eclipse.jgit.lib.ObjectId;
import org.unbescape.html.HtmlEscape;

import javax.annotation.Nullable;
import javax.inject.Inject;
import javax.inject.Singleton;
import javax.persistence.EntityNotFoundException;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.NotAcceptableException;
import javax.ws.rs.NotFoundException;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Api(internal = true)
@Path("/mcp")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Singleton
public class ModelContextProtocolResource {

    private final ObjectMapper objectMapper;

    private final SettingManager settingManager;

    private final UserManager userManager;

    private final IssueManager issueManager;

    private final ProjectManager projectManager;

    private final LinkSpecManager linkSpecManager;

    private final IssueLinkManager issueLinkManager;

    private final IssueCommentManager issueCommentManager;

    private final IterationManager iterationManager;

    private final IssueChangeManager issueChangeManager;

    private final IssueWorkManager issueWorkManager;

    private final SubscriptionManager subscriptionManager;

    private final PullRequestManager pullRequestManager;

    private final PullRequestChangeManager pullRequestChangeManager;

    private final PullRequestAssignmentManager pullRequestAssignmentManager;

    private final PullRequestReviewManager pullRequestReviewManager;

    private final PullRequestLabelManager pullRequestLabelManager;

    private final PullRequestCommentManager pullRequestCommentManager;

    private final GitService gitService;

    private final LabelSpecManager labelSpecManager;

    private final UrlManager urlManager;

    @Inject
    public ModelContextProtocolResource(ObjectMapper objectMapper, SettingManager settingManager,
                                        UserManager userManager, IssueManager issueManager, ProjectManager projectManager,
                                        LinkSpecManager linkSpecManager, IssueCommentManager issueCommentManager,
                                        IterationManager iterationManager, SubscriptionManager subscriptionManager,
                                        IssueChangeManager issueChangeManager, IssueLinkManager issueLinkManager,
                                        IssueWorkManager issueWorkManager, PullRequestManager pullRequestManager,
                                        PullRequestChangeManager pullRequestChangeManager, GitService gitService,
                                        LabelSpecManager labelSpecManager, PullRequestReviewManager pullRequestReviewManager,
                                        PullRequestAssignmentManager pullRequestAssignmentManager,
                                        PullRequestLabelManager pullRequestLabelManager, UrlManager urlManager,
                                        PullRequestCommentManager pullRequestCommentManager) {
        this.objectMapper = objectMapper;
        this.settingManager = settingManager;
        this.issueManager = issueManager;
        this.userManager = userManager;
        this.projectManager = projectManager;
        this.linkSpecManager = linkSpecManager;
        this.issueCommentManager = issueCommentManager;
        this.iterationManager = iterationManager;
        this.subscriptionManager = subscriptionManager;
        this.issueChangeManager = issueChangeManager;
        this.issueLinkManager = issueLinkManager;
        this.issueWorkManager = issueWorkManager;
        this.pullRequestManager = pullRequestManager;
        this.pullRequestChangeManager = pullRequestChangeManager;
        this.pullRequestAssignmentManager = pullRequestAssignmentManager;
        this.pullRequestReviewManager = pullRequestReviewManager;
        this.gitService = gitService;
        this.labelSpecManager = labelSpecManager;
        this.pullRequestLabelManager = pullRequestLabelManager;
        this.urlManager = urlManager;
        this.pullRequestCommentManager = pullRequestCommentManager;
    }

    private String getIssueQueryStringDescription() {
        var stateNames = new StringBuilder();
        for (var state: settingManager.getIssueSetting().getStateSpecs()) {
            stateNames.append("  - ");
            stateNames.append(state.getName());
            if (state.getDescription() != null) {
                stateNames.append(": ").append(state.getDescription().replace("\n", " "));
            }
            stateNames.append("\n");
        }
        var fieldCriterias = new StringBuilder();
        for (var field: settingManager.getIssueSetting().getFieldSpecs()) {
            if (field instanceof ChoiceField) {
                var choiceField = (ChoiceField) field;
                fieldCriterias.append("- ").append(field.getName().toLowerCase()).append(" criteria in form of: \"").append(field.getName()).append("\" is \"<").append(field.getName().toLowerCase()).append(" value>\" (quotes are required), where <").append(field.getName().toLowerCase()).append(" value> is one of below:\n");
                for (var choice : choiceField.getPossibleValues())
                    fieldCriterias.append("  - ").append(choice).append("\n");
            } else if (field instanceof UserChoiceField) {
                fieldCriterias.append("- ").append(field.getName().toLowerCase()).append(" criteria in form of: \"").append(field.getName()).append("\" is \"<login name of a user>\" (quotes are required)\n");
                fieldCriterias.append("- ").append(field.getName().toLowerCase()).append(" criteria for current user in form of: \"").append(field.getName()).append("\" is me (quotes are required)\n");
            } else if (field instanceof GroupChoiceField) {
                fieldCriterias.append("- ").append(field.getName().toLowerCase()).append(" criteria in form of: \"").append(field.getName()).append("\" is \"<group name>\" (quotes are required)\n");
            } else if (field instanceof BooleanField) {
                fieldCriterias.append("- ").append(field.getName().toLowerCase()).append(" is true criteria in form of: \"").append(field.getName()).append("\" is \"true\" (quotes are required)\n");
                fieldCriterias.append("- ").append(field.getName().toLowerCase()).append(" is false criteria in form of: \"").append(field.getName()).append("\" is \"false\" (quotes are required)\n");
            } else if (field instanceof DateField) {
                fieldCriterias.append("- ").append(field.getName().toLowerCase()).append(" is before certain date criteria in form of: \"").append(field.getName()).append("\" is before \"<date>\" (quotes are required), where <date> is of format YYYY-MM-DD\n");
                fieldCriterias.append("- ").append(field.getName().toLowerCase()).append(" is after certain date criteria in form of: \"").append(field.getName()).append("\" is after \"<date>\" (quotes are required), where <date> is of format YYYY-MM-DD\n");
            } else if (field instanceof DateTimeField) {
                fieldCriterias.append("- ").append(field.getName().toLowerCase()).append(" is before certain date time criteria in form of: \"").append(field.getName()).append("\" is before \"<date time>\" (quotes are required), where <date time> is of format YYYY-MM-DD HH:mm\n");
                fieldCriterias.append("- ").append(field.getName().toLowerCase()).append(" is after certain date time criteria in form of: \"").append(field.getName()).append("\" is after \"<date time>\" (quotes are required), where <date time> is of format YYYY-MM-DD HH:mm\n");
            } else if (field instanceof IntegerField) {
                fieldCriterias.append("- ").append(field.getName().toLowerCase()).append(" is equal to certain integer criteria in form of: \"").append(field.getName()).append("\" is \"<integer>\" (quotes are required), where <integer> is an integer\n");
                fieldCriterias.append("- ").append(field.getName().toLowerCase()).append(" is greater than certain integer criteria in form of: \"").append(field.getName()).append("\" is greater than \"<integer>\" (quotes are required), where <integer> is an integer\n");
                fieldCriterias.append("- ").append(field.getName().toLowerCase()).append(" is less than certain integer criteria in form of: \"").append(field.getName()).append("\" is less than \"<integer>\" (quotes are required), where <integer> is an integer\n");
            }
            fieldCriterias.append("- ").append(field.getName().toLowerCase()).append(" is not set criteria in form of: \"").append(field.getName()).append("\" is empty (quotes are required)\n");
        }
        var linkCriterias = new StringBuilder();
        for (var linkSpec: linkSpecManager.query()) {
            linkCriterias.append("- criteria to list issues with any ").append(linkSpec.getName().toLowerCase()).append(" issues matching certain criteria in form of: any \"").append(linkSpec.getName()).append("\" matching(another criteria) (quotes are required)\n");
            linkCriterias.append("- criteria to list issues with all ").append(linkSpec.getName().toLowerCase()).append(" issues matching certain criteria in form of: all \"").append(linkSpec.getName()).append("\" matching(another criteria) (quotes are required)\n");
            linkCriterias.append("- criteria to list issues with some ").append(linkSpec.getName().toLowerCase()).append(" issues in form of: has any \"").append(linkSpec.getName()).append("\" (quotes are required)\n");
            if (linkSpec.getOpposite() != null) {
                linkCriterias.append("- criteria to list issues with any ").append(linkSpec.getOpposite().getName().toLowerCase()).append(" issues matching certain criteria in form of: any \"").append(linkSpec.getOpposite().getName()).append("\" matching(another criteria) (quotes are required)\n");
                linkCriterias.append("- criteria to list issues with all ").append(linkSpec.getOpposite().getName().toLowerCase()).append(" issues matching certain criteria in form of: all \"").append(linkSpec.getOpposite().getName()).append("\" matching(another criteria) (quotes are required)\n");
                linkCriterias.append("- criteria to list issues with some ").append(linkSpec.getOpposite().getName().toLowerCase()).append(" issues in form of: has any \"").append(linkSpec.getOpposite().getName()).append("\" (quotes are required)\n");
            }
        }
        var orderFields = new StringBuilder();
        for (var field: Issue.SORT_FIELDS.keySet()) {
            orderFields.append("- ").append(field).append("\n");
        }

        var description = 
                "A query string is one of below criteria:\n" +
                "- issue with specified number in form of: \"Number\" is \"#<issue number>\", or in form of: \"Number\" is \"<project key>-<issue number>\" (quotes are required)\n" +
                "- criteria to check if title/description/comment contains specified text in form of: ~<containing text>~\n" +
                "- state criteria in form of: \"State\" is \"<state name>\" (quotes are required), where <state name> is one of below:\n" +
                stateNames + 
                fieldCriterias + 
                linkCriterias + 
                "- submitted by specified user criteria in form of: submitted by \"<login name of a user>\" (quotes are required)\n" +
                "- submitted by current user criteria in form of: submitted by me (quotes are required)\n" +
                "- submitted before certain date criteria in form of: \"Submit Date\" is until \"<date>\" (quotes are required), where <date> is of format YYYY-MM-DD HH:mm\n" +
                "- submitted after certain date criteria in form of: \"Submit Date\" is since \"<date>\" (quotes are required), where <date> is of format YYYY-MM-DD HH:mm\n" +
                "- updated before certain date criteria in form of: \"Last Activity Date\" is until \"<date>\" (quotes are required), where <date> is of format YYYY-MM-DD HH:mm\n" +
                "- updated after certain date criteria in form of: \"Last Activity Date\" is since \"<date>\" (quotes are required), where <date> is of format YYYY-MM-DD HH:mm\n" +
                "- confidential criteria in form of: confidential\n" +
                "- iteration criteria in form of: \"Iteration\" is \"<iteration name>\" (quotes are required)\n" +
                "- and criteria in form of <criteria1> and <criteria2>\n" + 
                "- or criteria in form of <criteria1> or <criteria2>. Note that \"and criteria\" takes precedence over \"or criteria\", use braces to group \"or criteria\" like \"(criteria1 or criteria2) and criteria3\" if you want to override precedence\n" +
                "- not criteria in form of not(<criteria>)\n" +
                "\n" +
                "And can optionally add order clause at end of query string in form of: order by \"<field1>\" <asc|desc>,\"<field2>\" <asc|desc>,... (quotes are required), where <field> is one of below:\n" +
                orderFields + 
                "\n" +
                "Leave empty to list all accessible issues";

        return HtmlEscape.escapeHtml5(description);
    }

    private String getPullRequestQueryStringDescription() {
        var orderFields = new StringBuilder();
        for (var field : PullRequest.SORT_FIELDS.keySet()) {
            orderFields.append("- ").append(field).append("\n");
        }

        var labelNames = labelSpecManager.query().stream().map(LabelSpec::getName).collect(Collectors.joining(", "));
        var mergeStrategyNames = Arrays.stream(MergeStrategy.values()).map(MergeStrategy::name).collect(Collectors.joining(", "));

        var description = 
                "A query string is one of below criteria:\n" +
                "- pull request with specified number in form of: \"Number\" is \"#<pull request number>\", or in form of: \"Number\" is \"<project key>-<pull request number>\" (quotes are required)\n" +
                "- criteria to check if title/description/comment contains specified text in form of: ~<containing text>~\n" +
                "- open criteria in form of: open\n" +
                "- merged criteria in form of: merged\n" +
                "- discarded criteria in form of: discarded\n" +
                "- source branch criteria in form of: \"Source Branch\" is \"<branch name>\" (quotes are required)\n" +
                "- target branch criteria in form of: \"Target Branch\" is \"<branch name>\" (quotes are required)\n" +
                "- merge strategy criteria in form of: \"Merge Strategy\" is \"<merge strategy>\" (quotes are required), where <merge strategy> is one of: " + mergeStrategyNames + "\n" +
                "- label criteria in form of: \"Label\" is \"<label name>\" (quotes are required), where <label name> is one of: " + labelNames + "\n" +
                "- ready to merge criteria in form of: ready to merge\n" +
                "- waiting for someone to review criteria in form of: has pending reviews\n" +
                "- some builds are unsuccessful criteria in form of: has unsuccessful builds\n" +
                "- some builds are not finished criteria in form of: has unfinished builds\n" +
                "- has merge conflicts criteria in form of: has merge conflicts\n" +
                "- assigned to specified user criteria in form of: assigned to \"<login name of a user>\" (quotes are required)\n" +
                "- approved by specified user criteria in form of: approved by \"<login name of a user>\" (quotes are required)\n" +
                "- to be reviewed by specified user criteria in form of: to be reviewed by \"<login name of a user>\" (quotes are required)\n" +
                "- to be changed by specified user criteria in form of: to be changed by \"<login name of a user>\" (quotes are required)\n" +
                "- to be merged by specified user criteria in form of: to be merged by \"<login name of a user>\" (quotes are required)\n" +
                "- requested for changes by specified user in form of: requested for changes by \"<login name of a user>\" (quotes are required)\n" +
                "- need action of specified user criteria in form of: need action by \"<login name of a user>\" (quotes are required)\n" +
                "- assigned to current user criteria in form of: assigned to me\n" +
                "- approved by current user criteria in form of: approved by me\n" +
                "- to be reviewed by current user criteria in form of: to be reviewed by me\n" +
                "- to be changed by current user criteria in form of: to be changed by me\n" +
                "- to be merged by current user criteria in form of: to be merged by me\n" +
                "- requested for changes by current user in form of: requested for changes by me\n" +
                "- requested for changes by any user criteria in form of: someone requested for changes\n" +
                "- need action of current user criteria in form of: need my action\n" +
                "- submitted by specified user criteria in form of: submitted by \"<login name of a user>\" (quotes are required)\n" +
                "- submitted by current user criteria in form of: submitted by me (quotes are required)\n" +
                "- submitted before certain date criteria in form of: \"Submit Date\" is until \"<date>\" (quotes are required), where <date> is of format YYYY-MM-DD HH:mm\n" +
                "- submitted after certain date criteria in form of: \"Submit Date\" is since \"<date>\" (quotes are required), where <date> is of format YYYY-MM-DD HH:mm\n" +
                "- updated before certain date criteria in form of: \"Last Activity Date\" is until \"<date>\" (quotes are required), where <date> is of format YYYY-MM-DD HH:mm\n" +
                "- updated after certain date criteria in form of: \"Last Activity Date\" is since \"<date>\" (quotes are required), where <date> is of format YYYY-MM-DD HH:mm\n" +
                "- closed (merged or discarded) before certain date criteria in form of: \"Close Date\" is until \"<date>\" (quotes are required), where <date> is of format YYYY-MM-DD HH:mm\n" +
                "- closed (merged or discarded) after certain date criteria in form of: \"Close Date\" is since \"<date>\" (quotes are required), where <date> is of format YYYY-MM-DD HH:mm\n" +
                "- includes specified issue criteria in form of: includes issue \"<issue reference>\" (quotes are required)\n" +
                "- includes specified commit criteria in form of: includes commit \"<commit hash>\" (quotes are required)\n" +                
                "- and criteria in form of <criteria1> and <criteria2>\n" +
                "- or criteria in form of <criteria1> or <criteria2>. Note that \"and criteria\" takes precedence over \"or criteria\", use braces to group \"or criteria\" like \"(criteria1 or criteria2) and criteria3\" if you want to override precedence\n" +
                "- not criteria in form of not(<criteria>)\n" +
                "\n" +
                "And can optionally add order clause at end of query string in form of: order by \"<field1>\" <asc|desc>,\"<field2>\" <asc|desc>,... (quotes are required), where <field> is one of below:\n" +
                orderFields +
                "\n" +
                "Leave empty to list all pull requests";

        return HtmlEscape.escapeHtml5(description);
    }

    private String getToolParamName(String fieldName) {
        return fieldName.replace(" ", "_");
    }

    private String appendDescription(String description, String additionalDescription) {
        if (!description.isEmpty()) {
            if (description.endsWith("."))
                return description + " " + additionalDescription;
            else
                return description + ". " + additionalDescription;
        } else {
            return additionalDescription;
        }
    }

    private Project getProject(String projectPath) {
        var project = projectManager.findByPath(projectPath);
        if (project == null)
            throw new NotFoundException("Project not found: " + projectPath);
        if (!SecurityUtils.canAccessProject(project))
            throw new UnauthorizedException("Unable to access project: " + projectPath);
        return project;
    }

    private Map<String, Object> getFieldProperties(FieldSpec field) {
        String fieldDescription;
        if (field.getDescription() != null)
            fieldDescription = field.getDescription().replace("\n", " ");
        else
            fieldDescription = "";
        if (field instanceof ChoiceField) {
            var choiceField = (ChoiceField) field;
            if (field.isAllowMultiple())
                fieldDescription = appendDescription(fieldDescription,
                        "Expects one or more of: " + String.join(", ", choiceField.getPossibleValues()));
            else
                fieldDescription = appendDescription(fieldDescription,
                        "Expects one of: " + String.join(", ", choiceField.getPossibleValues()));
        } else if (field instanceof UserChoiceField) {
            if (field.isAllowMultiple())
                fieldDescription = appendDescription(fieldDescription, "Expects user login names");
            else
                fieldDescription = appendDescription(fieldDescription, "Expects user login name");
        } else if (field instanceof GroupChoiceField) {
        } else if (field instanceof BooleanField) {
            fieldDescription = appendDescription(fieldDescription, "Expects boolean value, true or false");
        } else if (field instanceof IntegerField) {
            fieldDescription = appendDescription(fieldDescription, "Expects integer value");
        } else if (field instanceof FloatField) {
            fieldDescription = appendDescription(fieldDescription, "Expects float value");
        } else if (field instanceof DateField || field instanceof DateTimeField) {
            if (field.isAllowMultiple())
                fieldDescription = appendDescription(fieldDescription,
                        "Expects unix timestamps in milliseconds since epoch");
            else
                fieldDescription = appendDescription(fieldDescription,
                        "Expects unix timestamp in milliseconds since epoch");
        }

        fieldDescription = HtmlEscape.escapeHtml5(fieldDescription);
        
        var fieldProperties = new HashMap<String, Object>();
        if (field.isAllowMultiple()) {
            fieldProperties.putAll(getArrayProperties(fieldDescription));
        } else {
            fieldProperties.put("type", "string");
            fieldProperties.put("description", fieldDescription);
        }
        return fieldProperties;
    }

    private Map<String, Object> getArrayProperties(String description) {
        return Map.of(
            "type", "array",
            "items", Map.of("type", "string"),
            "uniqueItems", true,
            "description", description);
    }

    @Path("/get-tool-input-schemas")
    @GET
    public Map<String, Object> getToolInputSchemas(@QueryParam("currentProject") @NotNull String currentProjectPath) {
        if (SecurityUtils.getAuthUser() == null)
            throw new UnauthenticatedException();
        var currentProject = getProject(currentProjectPath);

        Project.push(currentProject);
        try {
            var inputSchemas = new HashMap<String, Object>();

            var queryIssuesInputSchema = new HashMap<String, Object>();
            var queryIssuesProperties = new HashMap<String, Object>();

            queryIssuesProperties.put("query", Map.of(
                "type", "string",
                "description", getIssueQueryStringDescription()));
            queryIssuesProperties.put("offset", Map.of(
                "type", "integer",
                "description", "start position for the query (optional, defaults to 0)"));
            queryIssuesProperties.put("count", Map.of(
                "type", "integer",
                "description", "number of issues to return (optional, defaults to 25, max 100)"));

            queryIssuesInputSchema.put("Type", "object");
            queryIssuesInputSchema.put("Properties", queryIssuesProperties);
            queryIssuesInputSchema.put("Required", new ArrayList<>());

            inputSchemas.put("queryIssues", queryIssuesInputSchema);

            var createIssueInputSchema = new HashMap<String, Object>();
            var createIssueProperties = new HashMap<String, Object>();
            createIssueProperties.put("title", Map.of(
                "type", "string", 
                "description", "title of the issue"));
            createIssueProperties.put("description", Map.of(
                "type", "string", 
                "description", "description of the issue"));
            createIssueProperties.put("confidential", Map.of(
                "type", "boolean", 
                "description", "whether the issue is confidential"));

            if (SecurityUtils.canScheduleIssues(currentProject)) {                
                createIssueProperties.put("iterations", getArrayProperties("iteration names"));

                if (subscriptionManager.isSubscriptionActive() && currentProject.isTimeTracking()) {
                    createIssueProperties.put("ownEstimatedTime", Map.of(
                        "type", "integer",
                        "description", "Estimated time in hours for this issue only (not including linked issues)"));
                }
            }

            var createIssueRequiredProperties = new ArrayList<String>();
            createIssueRequiredProperties.add("title");

            for (var field: settingManager.getIssueSetting().getFieldSpecs()) {
                if (field.isApplicable(currentProject) 
                        && field.isPromptUponIssueOpen() 
                        && SecurityUtils.canEditIssueField(currentProject, field.getName())) {
                    var paramName = getToolParamName(field.getName());
                    var fieldProperties = getFieldProperties(field);
                    createIssueProperties.put(paramName, fieldProperties);

                    if (!field.isAllowEmpty() && field.getShowCondition() == null) {
                        createIssueRequiredProperties.add(paramName);
                    }
                }
            }
            
            createIssueInputSchema.put("Type", "object");
            createIssueInputSchema.put("Properties", createIssueProperties);
            createIssueInputSchema.put("Required", createIssueRequiredProperties);

            inputSchemas.put("createIssue", createIssueInputSchema);

            var editIssueInputSchema = new HashMap<String, Object>();
            var editIssueProperties = new HashMap<String, Object>();
            editIssueProperties.put("issueReference", Map.of(
                "type", "string",
                "description", "reference of the issue to update"));
            editIssueProperties.put("title", Map.of(
                    "type", "string",
                    "description", "title of the issue"));
            editIssueProperties.put("description", Map.of(
                    "type", "string",
                    "description", "description of the issue"));
            editIssueProperties.put("confidential", Map.of(
                    "type", "boolean",
                    "description", "whether the issue is confidential"));

            if (SecurityUtils.canScheduleIssues(currentProject)) {
                editIssueProperties.put("iterations", getArrayProperties("iterations to schedule the issue in"));

                if (subscriptionManager.isSubscriptionActive() && currentProject.isTimeTracking()) {
                    editIssueProperties.put("ownEstimatedTime", Map.of(
                            "type", "integer",
                            "description", "Estimated time in hours for this issue only (not including linked issues)"));
                }
            }

            for (var field : settingManager.getIssueSetting().getFieldSpecs()) {
                if (SecurityUtils.canEditIssueField(currentProject, field.getName())) {
                    var paramName = getToolParamName(field.getName());

                    var fieldProperties = getFieldProperties(field);
                    editIssueProperties.put(paramName, fieldProperties);
               }
            }

            editIssueInputSchema.put("Type", "object");
            editIssueInputSchema.put("Properties", editIssueProperties);
            editIssueInputSchema.put("Required", List.of("issueReference"));

            inputSchemas.put("editIssue", editIssueInputSchema);            

            var toStates = new HashSet<String>();
            for (var transition: settingManager.getIssueSetting().getTransitionSpecs()) {
                if (transition instanceof ManualSpec) {
                    if (transition.getToStates().isEmpty()) {
                        toStates.addAll(settingManager.getIssueSetting().getStateSpecMap().keySet());
                        break;
                    } else {
                        toStates.addAll(transition.getToStates());
                    }
                }
            }
            if (!toStates.isEmpty()) {
                var transitionInputSchema = new HashMap<String, Object>();
                transitionInputSchema.put("Type", "object");

                var transitIssueProperties = new HashMap<String, Object>();
                transitIssueProperties.put("issueReference", Map.of(
                    "type", "string",
                    "description", "reference of the issue to transit state"));
                transitIssueProperties.put("state", Map.of(
                        "type", "string",
                        "description", "new state of the issue after transition. Must be one of: " + String.join(", ", toStates)));
                transitIssueProperties.put("comment", Map.of(
                        "type", "string",
                        "description", "comment of the transition"));

                for (var field : settingManager.getIssueSetting().getFieldSpecs()) {
                    if (SecurityUtils.canEditIssueField(currentProject, field.getName())) {
                        var paramName = getToolParamName(field.getName());

                        var fieldProperties = getFieldProperties(field);
                        transitIssueProperties.put(paramName, fieldProperties);
                    }
                }                

                transitionInputSchema.put("Properties", transitIssueProperties);
                transitionInputSchema.put("Required", List.of("issueReference", "state"));

                inputSchemas.put("transitIssue", transitionInputSchema);
            }

            var linkSpecs = linkSpecManager.query();
            if (!linkSpecs.isEmpty()) {
                var linkInputSchema = new HashMap<String, Object>();
                linkInputSchema.put("Type", "object");

                var linkProperties = new HashMap<String, Object>();
                linkProperties.put("sourceIssueReference", Map.of(
                    "type", "string", 
                    "description", "Issue reference as source of the link"));
                linkProperties.put("targetIssueReference", Map.of(
                    "type", "string", 
                    "description", "Issue reference as target of the link"
                ));
                var linkNames = new ArrayList<String>();
                for (var linkSpec: linkSpecs) {
                    linkNames.add(linkSpec.getName());
                    if (linkSpec.getOpposite() != null)
                        linkNames.add(linkSpec.getOpposite().getName());
                }
                linkProperties.put("linkName", Map.of(
                    "type", "string", 
                    "description", "Name of the link. Must be one of: " + String.join(", ", linkNames)));
                linkInputSchema.put("Properties", linkProperties);
                linkInputSchema.put("Required", List.of("sourceIssueReference", "targetIssueReference", "linkName"));

                inputSchemas.put("linkIssues", linkInputSchema);
            }

            var queryPullRequestsInputSchema = new HashMap<String, Object>();
            var queryPullRequestsProperties = new HashMap<String, Object>();

            queryPullRequestsProperties.put("query", Map.of(
                    "type", "string",
                    "description", getPullRequestQueryStringDescription()));
            queryPullRequestsProperties.put("offset", Map.of(
                    "type", "integer",
                    "description", "start position for the query (optional, defaults to 0)"));
            queryPullRequestsProperties.put("count", Map.of(
                    "type", "integer",
                    "description", "number of pull requests to return (optional, defaults to 25, max 100)"));

            queryPullRequestsInputSchema.put("Type", "object");
            queryPullRequestsInputSchema.put("Properties", queryPullRequestsProperties);
            queryPullRequestsInputSchema.put("Required", new ArrayList<>());

            inputSchemas.put("queryPullRequests", queryPullRequestsInputSchema);

            var createPullRequestInputSchema = new HashMap<String, Object>();
            var createPullRequestProperties = new HashMap<String, Object>();            
            createPullRequestProperties.put("title", Map.of(
                    "type", "string",
                    "description", "Title of the pull request"));
            createPullRequestProperties.put("description", Map.of(
                    "type", "string",
                    "description", "Description of the pull request"));
            createPullRequestProperties.put("targetBranch", Map.of(
                    "type", "string",
                    "description", "A branch in current project to be used as target branch of the pull request"));
            createPullRequestProperties.put("sourceBranch", Map.of(
                    "type", "string",
                    "description", "A branch in current project to be used as source branch of the pull request"));
            createPullRequestProperties.put("mergeStrategy", Map.of(
                    "type", "string",
                    "description", "Merge strategy of the pull request. Must be one of: " + 
                        Arrays.stream(MergeStrategy.values()).map(Enum::name).collect(Collectors.joining(", "))));
            createPullRequestProperties.put("reviewers", Map.of(
                    "type", "array",
                    "items", Map.of("type", "string"),
                    "uniqueItems", true,
                    "description", "Reviewers of the pull request. Expects user login names"));
            createPullRequestProperties.put("assignees", Map.of(
                    "type", "array",
                    "items", Map.of("type", "string"),
                    "uniqueItems", true,
                    "description", "Assignees of the pull request. Expects user login names"));

            var labelSpecs = labelSpecManager.query();
            if (!labelSpecs.isEmpty()) {
                createPullRequestProperties.put("labels", Map.of(
                        "type", "array",
                        "items", Map.of("type", "string"),
                        "uniqueItems", true,
                        "description", "Labels of the pull request. Must be one or more of: " + labelSpecs.stream().map(LabelSpec::getName).collect(Collectors.joining(", "))));
            }

            createPullRequestInputSchema.put("Type", "object");
            createPullRequestInputSchema.put("Properties", createPullRequestProperties);
            createPullRequestInputSchema.put("Required", List.of("title", "targetBranch", "sourceBranch"));

            inputSchemas.put("createPullRequest", createPullRequestInputSchema);

            var editPullRequestInputSchema = new HashMap<String, Object>();
            var editPullRequestProperties = new HashMap<String, Object>();
            editPullRequestProperties.put("pullRequestReference", Map.of(
                    "type", "string",
                    "description", "Reference of the pull request to edit"));
            editPullRequestProperties.put("title", Map.of(
                    "type", "string",
                    "description", "Title of the pull request"));
            editPullRequestProperties.put("description", Map.of(
                    "type", "string",
                    "description", "Description of the pull request"));
            editPullRequestProperties.put("mergeStrategy", Map.of(
                    "type", "string",
                    "description", "Merge strategy of the pull request. Must be one of: " +
                            Arrays.stream(MergeStrategy.values()).map(Enum::name).collect(Collectors.joining(", "))));
            editPullRequestProperties.put("assignees", Map.of(
                    "type", "array",
                    "items", Map.of("type", "string"),
                    "uniqueItems", true,
                    "description", "Assignees of the pull request. Expects user login names"));

            editPullRequestProperties.put("addReviewers", Map.of(
                    "type", "array",
                    "items", Map.of("type", "string"),
                    "uniqueItems", true,
                    "description", "Request review from specified users. Expects user login names"));
            editPullRequestProperties.put("removeReviewers", Map.of(
                    "type", "array",
                    "items", Map.of("type", "string"),
                    "uniqueItems", true,
                    "description", "Remove specified reviewers. Expects user login names"));

            if (!labelSpecs.isEmpty()) {
                editPullRequestProperties.put("labels", Map.of(
                    "type", "array",
                    "items", Map.of("type", "string"),
                    "uniqueItems", true,
                    "description", "Labels of the pull request. Must be one or more of: " + labelSpecs.stream().map(LabelSpec::getName).collect(Collectors.joining(", "))));
            }

            editPullRequestProperties.put("autoMerge", Map.of(
                    "type", "boolean",
                    "description", "Whether to enable auto merge"));
            editPullRequestProperties.put("autoMergeCommitMessage", Map.of(
                    "type", "string",
                    "description", "Preset commit message for auto merge"));

            editPullRequestInputSchema.put("Type", "object");
            editPullRequestInputSchema.put("Properties", editPullRequestProperties);
            editPullRequestInputSchema.put("Required", List.of("pullRequestReference"));

            inputSchemas.put("editPullRequest", editPullRequestInputSchema);

            return inputSchemas;
        } finally {
            Project.pop();
        }
    }

    @Path("/get-prompt-arguments")
    @GET
    public Map<String, Object> getPromptArguments(@QueryParam("currentProject") @NotNull String currentProjectPath) {
        if (SecurityUtils.getAuthUser() == null)
            throw new UnauthenticatedException();

        var currentProject = getProject(currentProjectPath);

        Project.push(currentProject);
        try {
            var arguments = new LinkedHashMap<String, Object>();
            var createIssueArguments = new ArrayList<Map<String, Object>>();
            createIssueArguments.add(Map.of(
                    "name", "title",
                    "description", "title of the issue",
                    "required", true));
            createIssueArguments.add(Map.of(
                    "name", "description",
                    "description", "description of the issue",
                    "required", false));
            createIssueArguments.add(Map.of(
                    "name", "confidential",
                    "description", "whether the issue is confidential",
                    "required", false));
            
            if (SecurityUtils.canScheduleIssues(currentProject)) {
                createIssueArguments.add(Map.of(
                        "name", "iterations",
                        "description", "iterations to schedule the issue in",
                        "required", false));

                if (subscriptionManager.isSubscriptionActive() && currentProject.isTimeTracking()) {
                    createIssueArguments.add(Map.of(
                        "name", "ownEstimatedTime",
                        "description", "estimated time for this issue only in hours (excluding linked issues)", 
                        "required", false));
                }
            }

            for (var field: settingManager.getIssueSetting().getFieldSpecs()) {
                if (field.isApplicable(currentProject) 
                        && field.isPromptUponIssueOpen()
                        && field.getShowCondition() == null
                        && SecurityUtils.canEditIssueField(currentProject, field.getName())) {
                    var paramName = getToolParamName(field.getName());
                    String description = "";
                    if (field.getDescription() != null)
                        description = field.getDescription().replace("\n", " ");
                    if (field instanceof ChoiceField) {
                        var choiceField = (ChoiceField) field;
                        if (field.isAllowMultiple())
                            description = appendDescription(description, "Expects one or more of: " + String.join(", ", choiceField.getPossibleValues()));
                        else
                            description = appendDescription(description, "Expects one of: " + String.join(", ", choiceField.getPossibleValues()));
                    }
                    var argumentMap = new HashMap<String, Object>();
                    argumentMap.put("name", paramName);
                    argumentMap.put("required", !field.isAllowEmpty());
                    argumentMap.put("description", HtmlEscape.escapeHtml5(description));
                    createIssueArguments.add(argumentMap);
                }
            }

            arguments.put("createIssue", createIssueArguments);
            return arguments;
        } finally {
            Project.pop();
        } 
    }

    @Path("/get-login-name")
    @GET
    public String getLoginName(@QueryParam("userName") String userName) {
        if (SecurityUtils.getAuthUser() == null)
            throw new UnauthenticatedException();

        User user;                
        userName = StringUtils.trimToNull(userName);
        if (userName != null) {
            user = userManager.findByName(userName);
            if (user == null)
                user = userManager.findByFullName(userName);
            if (user == null) {
                var matchingUsers = new ArrayList<User>();
                var lowerCaseUserName = userName.toLowerCase();
                for (var eachUser: userManager.query()) {
                    if (eachUser.getFullName() != null) {
                        if (Splitter.on(" ").trimResults().omitEmptyStrings().splitToList(eachUser.getFullName().toLowerCase()).contains(lowerCaseUserName)) {
                            matchingUsers.add(eachUser);
                        }
                    }
                }
                if (matchingUsers.size() == 1) {
                    user = matchingUsers.get(0);
                } else if (matchingUsers.size() > 1) {
                    throw new InvalidParamException("Multiple users found: " + userName);
                }
            }
            if (user == null) 
                throw new NotFoundException("User not found: " + userName);
        } else {
            user = SecurityUtils.getUser();
        }
        return user.getName();
    }

    @Path("/get-unix-timestamp")
    @GET
    public long getUnixTimestamp(@QueryParam("dateTimeDescription") @NotNull String dateTimeDescription) {
        if (SecurityUtils.getAuthUser() == null)
            throw new UnauthenticatedException();

        return DateUtils.parseRelaxed(dateTimeDescription).getTime();
    }

    @Path("/query-issues")
    @GET
    public List<Map<String, Object>> queryIssues(@QueryParam("currentProject") @NotNull String currentProjectPath, 
            @QueryParam("query") String query, @QueryParam("offset") int offset, @QueryParam("count") int count) {
        if (SecurityUtils.getAuthUser() == null)
            throw new UnauthenticatedException();

        if (count > RestConstants.MAX_PAGE_SIZE)
            throw new InvalidParamException("Count should not be greater than " + RestConstants.MAX_PAGE_SIZE);

        var currentProject = getProject(currentProjectPath);

        EntityQuery<Issue> parsedQuery;
        if (query != null) {
            var option = new IssueQueryParseOption();
            option.withCurrentUserCriteria(true);
            parsedQuery = IssueQuery.parse(currentProject, query, option, true);
        } else {
            parsedQuery = new IssueQuery();
        }

        var issues = new ArrayList<Map<String, Object>>();
        for (var issue : issueManager.query(new ProjectScope(currentProject, true, false), parsedQuery, true, offset, count)) {
            var issueMap = getIssueMap(currentProject, issue);
            for (var entry: issue.getFieldInputs().entrySet()) {
                issueMap.put(entry.getKey(), entry.getValue().getValues());
            }
            issueMap.put("link", urlManager.urlFor(issue));
            issues.add(issueMap);
        }
        return issues;
    }

    private Issue getIssue(Project currentProject, String referenceString) {
        var issueReference = IssueReference.of(referenceString, currentProject);
        var issue = issueManager.find(issueReference.getProject(), issueReference.getNumber());
        if (issue != null) {
            if (!SecurityUtils.canAccessIssue(issue))
                throw new UnauthorizedException("No permission to access issue: " + referenceString);
            return issue;
        } else {
            throw new NotFoundException("Issue not found: " + referenceString);
        }
    }

    private Map<String, Object> getIssueMap(Project currentProject, Issue issue) {
        var typeReference = new TypeReference<LinkedHashMap<String, Object>>() {};
        var issueMap = objectMapper.convertValue(issue, typeReference);
        issueMap.remove("id");
        issueMap.remove("stateOrdinal");
        issueMap.remove("uuid");
        issueMap.remove("messageId");
        issueMap.remove("pinDate");
        issueMap.remove("boardPosition");
        issueMap.remove("numberScopeId");
        issueMap.put("reference", issue.getReference().toString(currentProject));
        issueMap.remove("submitterId");
        issueMap.put("submitter", issue.getSubmitter().getName());
        issueMap.remove("projectId");
        issueMap.put("Project", issue.getProject().getPath());
        issueMap.remove("lastActivity");
        issueMap.entrySet().removeIf(entry -> entry.getKey().endsWith("Count"));
        return issueMap;
    }
    
    @Path("/get-issue")
    @GET
    public Map<String, Object> getIssue(@QueryParam("currentProject") @NotNull String currentProjectPath, 
                @QueryParam("reference") @NotNull String issueReference) {
        if (SecurityUtils.getAuthUser() == null)
            throw new UnauthenticatedException();

        var currentProject = getProject(currentProjectPath);
        var issue = getIssue(currentProject, issueReference);
                
        var issueMap = getIssueMap(currentProject, issue);
        for (var entry : issue.getFieldInputs().entrySet()) {
            issueMap.put(entry.getKey(), entry.getValue().getValues());
        }
        
        Map<String, Collection<String>> linkedIssues = new HashMap<>();
        for (var link: issue.getTargetLinks()) {
            linkedIssues.computeIfAbsent(link.getSpec().getName(), k -> new ArrayList<>())
                    .add(link.getTarget().getReference().toString(currentProject));
        }
        for (var link : issue.getSourceLinks()) {
            if (link.getSpec().getOpposite() != null) {
                linkedIssues.computeIfAbsent(link.getSpec().getOpposite().getName(), k -> new ArrayList<>())
                        .add(link.getSource().getReference().toString(currentProject));
            } else {
                linkedIssues.computeIfAbsent(link.getSpec().getName(), k -> new ArrayList<>())
                        .add(link.getSource().getReference().toString(currentProject));
            }
        }
        issueMap.putAll(linkedIssues);
        issueMap.put("link", urlManager.urlFor(issue));

        return issueMap;
    }

    @Path("/get-issue-comments")
    @GET
    public List<Map<String, Object>> getIssueComments(@QueryParam("currentProject") @NotNull String currentProjectPath, 
                @QueryParam("reference") @NotNull String issueReference) {
        if (SecurityUtils.getAuthUser() == null)
            throw new UnauthenticatedException();

        var currentProject = getProject(currentProjectPath);
        var issue = getIssue(currentProject, issueReference);
                
        var comments = new ArrayList<Map<String, Object>>();
        for (var comment : issue.getComments()) {
            var commentMap = new HashMap<String, Object>();
            commentMap.put("user", comment.getUser().getName());
            commentMap.put("date", comment.getDate());
            commentMap.put("content", comment.getContent());
            comments.add(commentMap);
        }
        return comments;
    }

    @Path("/add-issue-comment")
    @Consumes(MediaType.TEXT_PLAIN)
    @POST
    public String addIssueComment(@QueryParam("currentProject") String currentProjectPath, 
            @QueryParam("reference") @NotNull String issueReference, @NotNull String commentContent) {
        if (SecurityUtils.getAuthUser() == null)
            throw new UnauthenticatedException();

        var currentProject = getProject(currentProjectPath);
        var issue = getIssue(currentProject, issueReference);
        var comment = new IssueComment();
        comment.setIssue(issue);
        comment.setContent(commentContent);
        comment.setUser(SecurityUtils.getAuthUser());
        comment.setDate(new Date());
        issueCommentManager.create(comment);

        return "Commented on issue " + issueReference + ": " + urlManager.urlFor(comment);
    }

    @Path("/create-issue")
    @POST
    public String createIssue(@QueryParam("currentProject") @NotNull String currentProjectPath, 
                @NotNull @Valid Map<String, Serializable> data) {
        if (SecurityUtils.getAuthUser() == null)
            throw new UnauthenticatedException();

        var currentProject = getProject(currentProjectPath);

        normalizeIssueData(data);

        var issueSetting = settingManager.getIssueSetting();

        Issue issue = new Issue();
        var title = (String) data.remove("title");
        if (title == null)
            throw new InvalidParamException("title is required");
        issue.setTitle(title);
        var description = (String) data.remove("description");
        issue.setDescription(description);
        var confidential = (Boolean) data.remove("confidential");
        if (confidential != null)
            issue.setConfidential(confidential);

        Integer ownEstimatedTime = (Integer) data.remove("ownEstimatedTime");
        if (ownEstimatedTime != null) {
            if (!subscriptionManager.isSubscriptionActive())
                throw new NotAcceptableException("An active subscription is required for this feature");
            if (!currentProject.isTimeTracking())
                throw new NotAcceptableException("Time tracking needs to be enabled for the project");
            if (!SecurityUtils.canScheduleIssues(currentProject))
                throw new UnauthorizedException("Issue schedule permission required to set own estimated time");
            issue.setOwnEstimatedTime(ownEstimatedTime*60);
        }

        @SuppressWarnings("unchecked")
        List<String> iterationNames = (List<String>) data.remove("iterations");
        if (iterationNames != null) {
            if (!SecurityUtils.canScheduleIssues(currentProject))
                throw new UnauthorizedException("Issue schedule permission required to set iterations");
            for (var iterationName : iterationNames) {
                var iteration = iterationManager.findInHierarchy(currentProject, iterationName);
                if (iteration == null)
                    throw new NotFoundException("Iteration '" + iterationName + "' not found");
                IssueSchedule schedule = new IssueSchedule();
                schedule.setIssue(issue);
                schedule.setIteration(iteration);
                issue.getSchedules().add(schedule);
            }
        }

        issue.setProject(currentProject);
        issue.setSubmitDate(new Date());
        issue.setSubmitter(SecurityUtils.getAuthUser());
        issue.setState(issueSetting.getInitialStateSpec().getName());

        issue.setFieldValues(FieldUtils.getFieldValues(issue.getProject(), data));

        try {
            issueManager.open(issue);
        } catch (RequiredFieldMissingException e) {
            throw new InvalidParamException(e.getMessage());
        }

        return "Created issue " + issue.getReference().toString(currentProject) + ": " + urlManager.urlFor(issue);
    }

    @Path("/edit-issue")
    @POST
    public String editIssue(@QueryParam("currentProject") @NotNull String currentProjectPath, 
                @QueryParam("reference") @NotNull String issueReference, @NotNull Map<String, Serializable> data) {
        if (SecurityUtils.getAuthUser() == null)
            throw new UnauthenticatedException();

        var currentProject = getProject(currentProjectPath);

        var issue = getIssue(currentProject, issueReference);
        if (!SecurityUtils.canModifyIssue(issue))
            throw new UnauthorizedException();

        normalizeIssueData(data);

        var title = (String) data.remove("title");
        if (title != null) 
            issueChangeManager.changeTitle(issue, title);

        if (data.containsKey("description")) 
            issueChangeManager.changeDescription(issue, (String) data.remove("description"));

        var confidential = (Boolean) data.remove("confidential");
        if (confidential != null)
            issueChangeManager.changeConfidential(issue, confidential);

        Integer ownEstimatedTime = (Integer) data.remove("ownEstimatedTime");
        if (ownEstimatedTime != null) {
            if (!subscriptionManager.isSubscriptionActive())
                throw new NotAcceptableException("An active subscription is required for this feature");
            if (!issue.getProject().isTimeTracking())
                throw new NotAcceptableException("Time tracking needs to be enabled for the project");
            if (!SecurityUtils.canScheduleIssues(issue.getProject()))
                throw new UnauthorizedException("Issue schedule permission required to set own estimated time");
            issueChangeManager.changeOwnEstimatedTime(issue, ownEstimatedTime*60);
        }

        @SuppressWarnings("unchecked")
        List<String> iterationNames = (List<String>) data.remove("iterations");
        if (iterationNames != null) {
            if (!SecurityUtils.canScheduleIssues(issue.getProject()))
                throw new UnauthorizedException("Issue schedule permission required to set iterations");
            var iterations = new ArrayList<Iteration>();
            for (var iterationName : iterationNames) {
                var iteration = iterationManager.findInHierarchy(issue.getProject(), iterationName);
                if (iteration == null)
                    throw new NotFoundException("Iteration '" + iterationName + "' not found");
                iterations.add(iteration);
            }
            issueChangeManager.changeIterations(issue, iterations);
        }

        if (!data.isEmpty()) {
            var issueSetting = settingManager.getIssueSetting();
            String initialState = issueSetting.getInitialStateSpec().getName();

            if (!SecurityUtils.canManageIssues(issue.getProject())
                    && !(issue.getSubmitter().equals(SecurityUtils.getAuthUser())
                            && issue.getState().equals(initialState))) {
                throw new UnauthorizedException("No permission to update issue fields");
            }

            try {
                issueChangeManager.changeFields(issue, FieldUtils.getFieldValues(issue.getProject(), data));
            } catch (RequiredFieldMissingException e) {
                throw new InvalidParamException(e.getMessage());
            }
        }

        return "Edited issue " + issueReference + ": " + urlManager.urlFor(issue);
    }

    @Path("/transit-issue")
    @POST
    public String transitIssue(@QueryParam("currentProject") @NotNull String currentProjectPath, 
                @QueryParam("reference") @NotNull String issueReference, @NotNull Map<String, Serializable> data) {
        if (SecurityUtils.getAuthUser() == null)
            throw new UnauthenticatedException();

        var currentProject = getProject(currentProjectPath);

        var issue = getIssue(currentProject, issueReference);
        normalizeIssueData(data);
        var state = (String) data.remove("state");
        if (state == null)
            throw new InvalidParamException("state is required");
        var comment = (String) data.remove("comment");
        ManualSpec transition = settingManager.getIssueSetting().getManualSpec(issue, state);

        var fieldValues = FieldUtils.getFieldValues(issue.getProject(), data);
        try {
            issueChangeManager.changeState(issue, state, fieldValues, transition.getPromptFields(),
                    transition.getRemoveFields(), comment);
        } catch (RequiredFieldMissingException e) {
            throw new InvalidParamException(e.getMessage());
        }
        var feedback = "Issue " + issueReference + " transited to state \"" + state + "\": " + urlManager.urlFor(issue);
        var stateDescription = settingManager.getIssueSetting().getStateSpec(state).getDescription();
        if (stateDescription != null)
            feedback += "\n\n" + stateDescription;
        return feedback;
    }

    @Path("/link-issues")
    @GET
    public String linkIssues(@QueryParam("currentProject") @NotNull String currentProjectPath, 
            @QueryParam("sourceReference") @NotNull String sourceReference, 
            @QueryParam("linkName") @Nullable String linkName, 
            @QueryParam("targetReference") @NotNull String targetReference) {
        if (SecurityUtils.getAuthUser() == null)
            throw new UnauthenticatedException();

        var currentProject = getProject(currentProjectPath);
        var sourceIssue = getIssue(currentProject, sourceReference);
        var targetIssue = getIssue(currentProject, targetReference);
        var linkSpec = linkSpecManager.find(linkName);
        if (linkSpec == null)
            throw new NotFoundException("Link spec not found: " + linkName);
        if (!SecurityUtils.canEditIssueLink(sourceIssue.getProject(), linkSpec) 
                || !SecurityUtils.canEditIssueLink(targetIssue.getProject(), linkSpec)) {
            throw new UnauthorizedException("No permission to add specified link for specified issues");
        }
        
        var link = new IssueLink();
        link.setSpec(linkSpec);
        if (Objects.equals(linkName, linkSpec.getName())) {
            link.setSource(sourceIssue);
            link.setTarget(targetIssue);
        } else {
            link.setSource(targetIssue);
            link.setTarget(sourceIssue);
        }
        try {
            link.checkValid();
        } catch (InvalidIssueLinkException e) {
            throw new NotAcceptableException(e.getMessage());
        }
        issueLinkManager.create(link);

        return "Issue " + targetReference + " added as \"" + linkName + "\" of " + sourceReference + ": " + urlManager.urlFor(sourceIssue);
    }

    @Path("/log-work")
    @Consumes(MediaType.TEXT_PLAIN)
    @POST
    public String logWork(@QueryParam("currentProject") @NotNull String currentProjectPath, 
            @QueryParam("reference") @NotNull String issueReference, 
            @QueryParam("spentHours") int spentHours, String comment) {
        if (SecurityUtils.getAuthUser() == null)
            throw new UnauthenticatedException();
        var currentProject = getProject(currentProjectPath);
        var issue = getIssue(currentProject, issueReference);

        if (!subscriptionManager.isSubscriptionActive())
            throw new NotAcceptableException("An active subscription is required for this feature");
        if (!issue.getProject().isTimeTracking())
            throw new NotAcceptableException("Time tracking needs to be enabled for the project");
        if (!SecurityUtils.canAccessIssue(issue))
            throw new UnauthorizedException("No permission to access issue: " + issueReference);

        var work = new IssueWork();
        work.setIssue(issue);
        work.setUser(SecurityUtils.getAuthUser());
        work.setMinutes(spentHours * 60);
        work.setNote(StringUtils.trimToNull(comment));
        issueWorkManager.createOrUpdate(work);
        return "Work logged for issue " + issueReference + ": " + urlManager.urlFor(issue);
    }

    private void normalizeIssueData(Map<String, Serializable> data) {
        for (var entry: data.entrySet()) {
            if (entry.getValue() instanceof String) 
                entry.setValue(StringUtils.trimToNull((String) entry.getValue()));
        }
        for (var field: settingManager.getIssueSetting().getFieldSpecs()) {
            var paramName = getToolParamName(field.getName());
            if (!paramName.equals(field.getName()) && data.containsKey(paramName)) {
                data.put(field.getName(), data.get(paramName));
                data.remove(paramName);
            }
        }        
    }    

    private Map<String, Object> getPullRequestMap(Project currentProject, PullRequest pullRequest) {
        var typeReference = new TypeReference<LinkedHashMap<String, Object>>() {};
        var pullRequestMap = objectMapper.convertValue(pullRequest, typeReference);
        pullRequestMap.remove("id");
        pullRequestMap.remove("uuid");
        pullRequestMap.remove("baseCommitHash");
        pullRequestMap.remove("buildCommitHash");
        pullRequestMap.remove("submitTimeGroups");
        pullRequestMap.remove("closeTimeGroups");
        pullRequestMap.remove("checkError");
        pullRequestMap.remove("numberScopeId");
        pullRequestMap.put("reference", pullRequest.getReference().toString(currentProject));
        pullRequestMap.remove("submitterId");
        pullRequestMap.put("submitter", pullRequest.getSubmitter().getName());
        pullRequestMap.remove("targetProjectId");        
        pullRequestMap.put("targetProject", pullRequest.getTarget().getProject().getPath());
        pullRequestMap.remove("sourceProjectId");
        if (pullRequest.getSourceProject() != null)
            pullRequestMap.put("sourceProject", pullRequest.getSourceProject().getPath());
        pullRequestMap.remove("codeCommentsUpdateDate");
        pullRequestMap.remove("lastActivity");
        pullRequestMap.entrySet().removeIf(entry -> entry.getKey().endsWith("Count"));
        return pullRequestMap;
    }

    @Path("/query-pull-requests")
    @GET
    public List<Map<String, Object>> queryPullRequests(@QueryParam("currentProject") @NotNull String currentProjectPath, 
            @QueryParam("query") String query, @QueryParam("offset") int offset, @QueryParam("count") int count) {
        if (SecurityUtils.getAuthUser() == null)
            throw new UnauthenticatedException();

        if (!SecurityUtils.canReadCode(getProject(currentProjectPath)))
            throw new UnauthorizedException("Code read permission required to query pull requests");

        if (count > RestConstants.MAX_PAGE_SIZE)
            throw new InvalidParamException("Count should not be greater than " + RestConstants.MAX_PAGE_SIZE);

        var currentProject = getProject(currentProjectPath);

        EntityQuery<PullRequest> parsedQuery;
        if (query != null) {
            parsedQuery = PullRequestQuery.parse(currentProject, query, true);
        } else {
            parsedQuery = new PullRequestQuery();
        }

        var pullRequests = new ArrayList<Map<String, Object>>();
        for (var pullRequest : pullRequestManager.query(currentProject, parsedQuery, false, offset, count)) {
            var pullRequestMap = getPullRequestMap(currentProject, pullRequest);
            pullRequestMap.put("link", urlManager.urlFor(pullRequest));
            pullRequests.add(pullRequestMap);
        }
        return pullRequests;
    }

    @Path("/get-pull-request")
    @GET
    public Map<String, Object> getPullRequest(@QueryParam("currentProject") @NotNull String currentProjectPath, 
                @QueryParam("reference") @NotNull String pullRequestReference) {
        if (SecurityUtils.getAuthUser() == null)
            throw new UnauthenticatedException();

        var currentProject = getProject(currentProjectPath);
        var pullRequest = getPullRequest(currentProject, pullRequestReference);
                
        var pullRequestMap = getPullRequestMap(currentProject, pullRequest);        
        pullRequestMap.put("assignees", pullRequest.getAssignees().stream().map(User::getName).collect(Collectors.toList()));
        var reviews = new ArrayList<Map<String, Object>>();
        for (var review : pullRequest.getReviews()) {
            if (review.getStatus() == PullRequestReview.Status.EXCLUDED)
                continue;
            var reviewMap = new HashMap<String, Object>();
            reviewMap.put("reviewer", review.getUser().getName());
            reviewMap.put("status", review.getStatus());
            reviews.add(reviewMap);
        }
        pullRequestMap.put("reviews", reviews);
        pullRequestMap.put("labels", pullRequest.getLabels().stream().map(it->it.getSpec().getName()).collect(Collectors.toList()));
        pullRequestMap.put("link", urlManager.urlFor(pullRequest));

        return pullRequestMap;
    }

    @Path("/get-pull-request-comments")
    @GET
    public List<Map<String, Object>> getPullRequestComments(@QueryParam("currentProject") @NotNull String currentProjectPath, 
                @QueryParam("reference") @NotNull String pullRequestReference) {
        if (SecurityUtils.getAuthUser() == null)
            throw new UnauthenticatedException();

        var currentProject = getProject(currentProjectPath);
        var pullRequest = getPullRequest(currentProject, pullRequestReference);
                
        var comments = new ArrayList<Map<String, Object>>();
        for (var comment : pullRequest.getComments()) {
            var commentMap = new HashMap<String, Object>();
            commentMap.put("user", comment.getUser().getName());
            commentMap.put("date", comment.getDate());
            commentMap.put("content", comment.getContent());
            comments.add(commentMap);
        }
        return comments;
    }

    @Path("/get-pull-request-code-comments")
    @GET
    public List<Map<String, Object>> getPullRequestCodeComments(@QueryParam("currentProject") @NotNull String currentProjectPath, 
                @QueryParam("reference") @NotNull String pullRequestReference) {
        if (SecurityUtils.getAuthUser() == null)
            throw new UnauthenticatedException();

        var currentProject = getProject(currentProjectPath);
        var pullRequest = getPullRequest(currentProject, pullRequestReference);
                
        var comments = new ArrayList<Map<String, Object>>();
        for (var comment : pullRequest.getCodeComments()) {
            var commentMap = new HashMap<String, Object>();
            commentMap.put("user", comment.getUser().getName());
            commentMap.put("date", comment.getCreateDate());
            commentMap.put("file", comment.getMark().getPath());
            commentMap.put("content", comment.getContent());
            commentMap.put("replies", comment.getReplies().size());
            commentMap.put("status", comment.isResolved()?"resolved":"unresolved");
            commentMap.put("link", urlManager.urlFor(comment));
            comments.add(commentMap);
        }
        return comments;
    }

    @Path("/get-pull-request-checkout-instruction")
    @GET
    @Produces(MediaType.TEXT_PLAIN)
    public String getPullRequestCheckoutInstruction(
                @QueryParam("currentProject") @NotNull String currentProjectPath, 
                @QueryParam("reference") @NotNull String pullRequestReference) {
        var user = SecurityUtils.getAuthUser();
        if (user == null)
            throw new UnauthenticatedException();
        var currentProject = getProject(currentProjectPath);
        var pullRequest = getPullRequest(currentProject, pullRequestReference);
        String localBranch;
        String remoteBranch;
        if (currentProject.equals(pullRequest.getSourceProject()) && pullRequest.isOpen()) {
            localBranch = pullRequest.getSourceBranch();
            remoteBranch = pullRequest.getSourceBranch();
        } else {
            localBranch = "pr-" + pullRequest.getNumber();
            remoteBranch = null;
        }

        var checkoutInstruction = new StringBuilder();
        checkoutInstruction.append("1. Fetch commit ").append(pullRequest.getLatestUpdate().getHeadCommitHash()).append("\n");
        checkoutInstruction.append("2. If local branch ").append(localBranch).append(" does not exist, create it off above commit; otherwise fastforward it to above commit\n");
        if (remoteBranch != null) 
            checkoutInstruction.append("3. Set up above local branch to track remote branch ").append(remoteBranch).append(". Make sure the remote tracking branch is reset to above commit\n");
        return checkoutInstruction.toString();
    }

    @Path("/get-pull-request-patch-info")
    @GET
    public Map<String, String> getPullRequestPatchInfo(
                @QueryParam("currentProject") @NotNull String currentProjectPath, 
                @QueryParam("reference") @NotNull String pullRequestReference, 
                @QueryParam("sinceLastReview") boolean sinceLastReview) {
        var user = SecurityUtils.getAuthUser();
        if (user == null)
            throw new UnauthenticatedException();
        var currentProject = getProject(currentProjectPath);
        var pullRequest = getPullRequest(currentProject, pullRequestReference);
        var oldCommitHash = pullRequest.getBaseCommitHash();
        if (sinceLastReview) {
            Date sinceDate = null;
            for (var change: pullRequest.getChanges()) {
                if ((sinceDate == null || change.getDate().after(sinceDate)) 
                        && change.getUser().equals(user)
                        && (change.getData() instanceof PullRequestApproveData || change.getData() instanceof PullRequestRequestedForChangesData)) {
                    sinceDate = change.getDate();
                }
            }
            for (var comment: pullRequest.getComments()) {
                if ((sinceDate == null || comment.getDate().after(sinceDate)) 
                        && comment.getUser().equals(user)) {
                    sinceDate = comment.getDate();
                }
            }
            if (sinceDate != null) {
                for (PullRequestUpdate update: pullRequest.getSortedUpdates()) {
                    if (update.getDate().before(sinceDate))
                        oldCommitHash = update.getHeadCommitHash();
                }
            }
        }
        var newCommitHash = pullRequest.getLatestUpdate().getHeadCommitHash();
        var comparisonBase = pullRequestManager.getComparisonBase(
            pullRequest, ObjectId.fromString(oldCommitHash), ObjectId.fromString(newCommitHash));

        var patchInfo = new HashMap<String, String>();
        patchInfo.put("projectId", currentProject.getId().toString());
        patchInfo.put("oldCommitHash", comparisonBase.name());
        patchInfo.put("newCommitHash", newCommitHash);        
        return patchInfo;
    }

    @Path("/create-pull-request")
    @POST
    public String createPullRequest(@QueryParam("currentProject") @NotNull String currentProjectPath,
                @NotNull Map<String, Serializable> data) {
        if (SecurityUtils.getAuthUser() == null)
            throw new UnauthenticatedException();
        var currentProject = getProject(currentProjectPath);        
        if (!SecurityUtils.canReadCode(currentProject))
            throw new UnauthorizedException("No permission to read code of project: " + currentProjectPath);

        normalizePullRequestData(data);

        var targetBranch = (String) data.remove("targetBranch");
        var sourceBranch = (String) data.remove("sourceBranch");

        var target = new ProjectAndBranch(currentProject, targetBranch);
        var source = new ProjectAndBranch(currentProject, sourceBranch);

        if (target.equals(source))
            throw new InvalidParamException("Target and source branches are the same");

        PullRequest request = pullRequestManager.findOpen(target, source);
        if (request != null)
            throw new InvalidParamException("Another pull request already opened for this change");

        request = pullRequestManager.findEffective(target, source);
        if (request != null) {
            if (request.isOpen())
                throw new InvalidParamException("Another pull request already opened for this change");
            else
                throw new InvalidParamException("Change already merged");
        }

        request = new PullRequest();
        ObjectId baseCommitId = gitService.getMergeBase(
                target.getProject(), target.getObjectId(),
                source.getProject(), source.getObjectId());

        if (baseCommitId == null)
            throw new InvalidParamException("No common base for source and target branches");

        request.setTitle((String) data.remove("title"));
        request.setTarget(target);
        request.setSource(source);
        request.setSubmitter(SecurityUtils.getAuthUser());
        request.setBaseCommitHash(baseCommitId.name());
        request.setDescription((String) data.remove("description"));

        var mergeStrategyName = (String) data.remove("mergeStrategy");
        if (mergeStrategyName != null) 
            request.setMergeStrategy(MergeStrategy.valueOf(mergeStrategyName));
        else
            request.setMergeStrategy(request.getProject().findDefaultPullRequestMergeStrategy());

        if (request.getBaseCommitHash().equals(source.getObjectName()))
            throw new InvalidParamException("Change already merged");

        PullRequestUpdate update = new PullRequestUpdate();
        
        update.setDate(Date.from(request.getSubmitDate().toInstant().plusSeconds(1)));
        update.setRequest(request);
        update.setHeadCommitHash(source.getObjectName());
        update.setTargetHeadCommitHash(request.getTarget().getObjectName());
        request.getUpdates().add(update);

        pullRequestManager.checkReviews(request, false);

        @SuppressWarnings("unchecked")
        var reviewerNames = (List<String>) data.remove("reviewers");
        if (reviewerNames != null) {
            for (var reviewerName : reviewerNames) {
                User reviewer = userManager.findByName(reviewerName);
                if (reviewer == null)
                    throw new NotFoundException("Reviewer not found: " + reviewerName);
                if (reviewer.equals(request.getSubmitter()))
                    throw new InvalidParamException("Pull request submitter can not be reviewer");

                if (request.getReview(reviewer) == null) {
                    PullRequestReview review = new PullRequestReview();
                    review.setRequest(request);
                    review.setUser(reviewer);
                    request.getReviews().add(review);
                }
            }
        }

        @SuppressWarnings("unchecked")
        var assigneeNames = (List<String>) data.remove("assignees");
        if (assigneeNames != null) {
            for (var assigneeName : assigneeNames) {
                User assignee = userManager.findByName(assigneeName);
                if (assignee == null)
                    throw new NotFoundException("Assignee not found: " + assigneeName);
                PullRequestAssignment assignment = new PullRequestAssignment();
                assignment.setRequest(request);
                assignment.setUser(assignee);
                request.getAssignments().add(assignment);
            }
        } else {
            for (var assignee : target.getProject().findDefaultPullRequestAssignees()) {
                PullRequestAssignment assignment = new PullRequestAssignment();
                assignment.setRequest(request);
                assignment.setUser(assignee);
                request.getAssignments().add(assignment);
            }
        }

        pullRequestManager.open(request);

        return "Created pull request " + request.getReference().toString(currentProject) + ": " + urlManager.urlFor(request);
    }

    private PullRequest getPullRequest(Project currentProject, String referenceString) {
        var requestReference = PullRequestReference.of(referenceString, currentProject);
        var request = pullRequestManager.find(requestReference.getProject(), requestReference.getNumber());
        if (request != null) {
            if (!SecurityUtils.canReadCode(request.getProject()))
                throw new UnauthorizedException("No permission to access pull request: " + referenceString);
            return request;
        } else {
            throw new NotFoundException("Pull request not found: " + referenceString);
        }
    }

    @SuppressWarnings("unchecked")
    @Path("/edit-pull-request")
    @POST
    public String editPullRequest(@QueryParam("currentProject") @NotNull String currentProjectPath,
            @QueryParam("reference") @NotNull String requestReference, @NotNull Map<String, Serializable> data) {
        var user = SecurityUtils.getAuthUser();
        if (user == null)
            throw new UnauthenticatedException();
        var currentProject = getProject(currentProjectPath);
        var request = getPullRequest(currentProject, requestReference);

        if (!SecurityUtils.canModifyPullRequest(request))
            throw new UnauthorizedException("No permission to edit pull request: " + requestReference);

        normalizePullRequestData(data);

        var title = (String) data.remove("title");
        if (title != null) 
            pullRequestChangeManager.changeTitle(request, title);

        if (data.containsKey("description")) 
            pullRequestChangeManager.changeDescription(request, (String) data.remove("description"));

        var labelNames = (List<String>) data.remove("labels");
        if (labelNames != null) {
            try {
                pullRequestLabelManager.sync(request, labelNames);
            } catch (EntityNotFoundException e) {
                throw new NotFoundException(e.getMessage());
            }
        }

        var mergeStrategyName = (String) data.remove("mergeStrategy");
        if (mergeStrategyName != null) {
            if (!request.isOpen())
                throw new NotAcceptableException("Pull request is closed");
            pullRequestChangeManager.changeMergeStrategy(request, MergeStrategy.valueOf(mergeStrategyName));
        }

        var assigneeNames = (List<String>) data.remove("assignees");
        if (assigneeNames != null) {                        
            if (!request.isOpen())
                throw new NotAcceptableException("Pull request is closed");
            for (var assigneeName : assigneeNames) {
                User assignee = userManager.findByName(assigneeName);
                if (assignee == null)
                    throw new NotFoundException("Assignee not found: " + assigneeName);
                if (request.getAssignments().stream().noneMatch(it -> it.getUser().equals(assignee))) {
                    PullRequestAssignment assignment = new PullRequestAssignment();
                    assignment.setRequest(request);
                    assignment.setUser(assignee);
                    pullRequestAssignmentManager.create(assignment);
                }
            }
            for (var assignee : request.getAssignments()) {
                if (assigneeNames.stream().noneMatch(it -> it.equals(assignee.getUser().getName()))) {
                    pullRequestAssignmentManager.delete(assignee);
                }
            }
        }

        var addReviewerNames = (List<String>) data.remove("addReviewers");
        if (addReviewerNames != null) {
            if (!request.isOpen())
                throw new NotAcceptableException("Pull request is closed");
            for (var reviewerName : addReviewerNames) {
                User reviewer = userManager.findByName(reviewerName);
                if (reviewer == null)
                    throw new NotFoundException("Reviewer not found: " + reviewerName);
                var review = request.getReview(reviewer);
                if (review == null) {
                    review = new PullRequestReview();
                    review.setRequest(request);
                    review.setUser(reviewer);
                    request.getReviews().add(review);
                    pullRequestReviewManager.createOrUpdate(review);
                } else if (review.getStatus() != PullRequestReview.Status.PENDING) {
                    review.setStatus(PullRequestReview.Status.PENDING);
                    pullRequestReviewManager.createOrUpdate(review);
                }
            }
        }
        var removeReviewerNames = (List<String>) data.remove("removeReviewers");
        if (removeReviewerNames != null) {
            if (!request.isOpen())
                throw new NotAcceptableException("Pull request is closed");
            var excludedReviews = new ArrayList<PullRequestReview>();
            for (var reviewerName : removeReviewerNames) {
                User reviewer = userManager.findByName(reviewerName);
                if (reviewer == null)
                    throw new NotFoundException("Reviewer not found: " + reviewerName);
                var review = request.getReview(reviewer);
                if (review != null && review.getStatus() != PullRequestReview.Status.EXCLUDED) {
                    review.setStatus(PullRequestReview.Status.EXCLUDED);
                    excludedReviews.add(review);
                }
            }
            pullRequestManager.checkReviews(request, false);
            var requiredReviewers = excludedReviews.stream()
                    .filter(it -> it.getStatus() != PullRequestReview.Status.EXCLUDED)
                    .map(it -> it.getUser().getName())
                    .collect(Collectors.toList());
            if (!requiredReviewers.isEmpty())
                throw new NotAcceptableException("Unable to remove mandatory reviewers: " + String.join(", ", requiredReviewers));
            for (var review : excludedReviews) 
                pullRequestReviewManager.createOrUpdate(review);
        }


        var autoMergeEnabled = (Boolean) data.remove("autoMerge");
        if (autoMergeEnabled != null) {
            if (!SecurityUtils.canWriteCode(request.getProject()))
                throw new UnauthorizedException("Code write permission is required to edit auto merge");
            if (!request.isOpen())
                throw new NotAcceptableException("Pull request is closed");

            if (autoMergeEnabled && request.checkMerge() == null) 
                throw new NotAcceptableException("This pull request is not eligible for auto-merge, as it can be merged directly now");

            var autoMerge = new AutoMerge();
            autoMerge.setEnabled(autoMergeEnabled);
            autoMerge.setCommitMessage(StringUtils.trimToNull((String) data.remove("autoMergeCommitMessage")));
            autoMerge.setUser(user);
            var errorMessage = request.checkMergeCommitMessage(user, request, autoMerge.getCommitMessage());
            if (errorMessage != null) 
                throw new NotAcceptableException("Error validating param auto merge commit message: " + errorMessage);

            pullRequestChangeManager.changeAutoMerge(request, autoMerge);
        }
                    
        return "Edited pull request " + request.getReference().toString(currentProject) + ": " + urlManager.urlFor(request);
    }    

    @Path("/review-pull-request")
    @Consumes(MediaType.TEXT_PLAIN)
    @POST
    public String reviewPullRequest(@QueryParam("currentProject") String currentProjectPath, 
            @QueryParam("reference") @NotNull String pullRequestReference, 
            @QueryParam("approved") boolean approved, String comment) {
        var user = SecurityUtils.getAuthUser();
        if (user == null)
            throw new UnauthenticatedException();

        var currentProject = getProject(currentProjectPath);
        var pullRequest = getPullRequest(currentProject, pullRequestReference);
        try {
            pullRequestReviewManager.review(pullRequest, approved, comment);
        } catch (ReviewRejectException e) {
            throw new NotAcceptableException(e.getMessage());
        }

        if (approved) {
            return "Approved pull request " + pullRequestReference + ": " + urlManager.urlFor(pullRequest);
        } else {
            return "Requested changes on pull request " + pullRequestReference + ": " + urlManager.urlFor(pullRequest);
        }
    }

    @Path("/add-pull-request-comment")
    @Consumes(MediaType.TEXT_PLAIN)
    @POST
    public String addPullRequestComment(@QueryParam("currentProject") String currentProjectPath, 
            @QueryParam("reference") @NotNull String pullRequestReference, @NotNull String commentContent) {
        if (SecurityUtils.getAuthUser() == null)
            throw new UnauthenticatedException();

        var currentProject = getProject(currentProjectPath);
        var pullRequest = getPullRequest(currentProject, pullRequestReference);
        var comment = new PullRequestComment();
        comment.setRequest(pullRequest);
        comment.setContent(commentContent);
        comment.setUser(SecurityUtils.getAuthUser());
        comment.setDate(new Date());
        pullRequestCommentManager.create(comment);

        return "Commented on pull request " + pullRequestReference + ": " + urlManager.urlFor(comment);
    }
    
    private void normalizePullRequestData(Map<String, Serializable> data) {
        for (var entry : data.entrySet()) {
            if (entry.getValue() instanceof String)
                entry.setValue(StringUtils.trimToNull((String) entry.getValue()));
        }
    }

}
