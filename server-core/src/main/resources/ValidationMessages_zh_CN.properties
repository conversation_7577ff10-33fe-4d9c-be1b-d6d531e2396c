AccessTokenEditBean.Authorizations.message=至少选择1项
MayNotBeEmpty.message=不应为空
MustNotBeEmpty.message=不能为空
GroovyScript.Name.message=名称不能以'builtin:'开头
Authenticator.UserSearchBases.message=至少指定1个
LdapAuthenticator.gr.message=不应该为空
MailService.timeout.message=不能小于5
InboxPollSetting.pollInterval.message=不能小于10
AtLeastOneEntryShouldBeSpecified=至少选择1项
ChooseAColor=请选择一种颜色
NeedTwoColums=请至少选择两个值
AtLeastOneStateNeedToSpecified=请至少选择1个状态
AtLeastOneValueNeedToSpecified=请至少选择1个值
AtLeastOneChoiceNeedToSpecified=请至少选择1项
ChooseRevision=请先选择一个基础版本来创建分支
ChooseRevisionForCreateTag=请先选择一个基础版本来创建git标签
AtLeastOneRepoShoudBeSelected=请至少选择一个仓库
AtLeastOneProjectShoudBeSelected=请至少选择一个项目
JobSecret.name.message=名称不允许含有'@'
JobProperty.name.message=名称不允许含有'@'
WebHook.eventType.message=请至少选择一个事件类型
ShouldNotBeLessThanOne=该值不能小于1
AtLeastOneOptionNeedsToBeSelected=至少要选一项
Project.serviceDescEmailAddress.message=服务台邮件地址的名称部分不允许含有'~'
Service.runAs.message=应以<uid>:<gid>的形式指定
OnlyHttpOrHttpsProtocolIsSupported=只支持 http/https 协议
Reserved.message=是系统保留的
StartAndEndWithAlphanumericOrNnderscore=应该以字母、数字或下划线开头和结尾。中间仅允许使用字母、数字、下划线(_)、连字符(-)、空格和点(.)。
TimesheetEditBean.nameAlreadyUsed.message=该名称已被使用
TrivyScanStep.detectOption.message=请至少指定一个探测项
AtLeastOneStateShouldBeSpecified=请至少指定一个状态
MalformedQuery.message=查询格式错误
StartAndEndWithAlphanumericOrNnderscoreButNoSpaceInMiddle=应该以字母、数字或下划线开头和结尾。中间仅允许使用字母、数字、下划线(_)、连字符(-)和点(.)。
ProjectPath.message=应该以字母、数字或下划线开头和结尾。中间仅允许使用正斜杠(/)、字母、数字、下划线(_)、连字符(-)和点(.)。
InvalidGitBranchName=无效的git分支名
StartWithAlphanumericOrNnderscore=应以字母、数字或下划线开头，且仅包含字母、数字、下划线(_)、连字符(-)或点(.)
ShouldBeTwoOrMoreUppercaseLetters=应为2个及以上的大写字母
MalformedPatternSet.message=模式串格式错误
MalformedGroovyTemplate.message=groovy模版格式错误
FailedCompileGroovyScript.message=groovy脚本编译失败
NotAValidCommitHash=无效的提交哈希
InvalidCronExpression=无效的cron表达式
CurrentPasswordDoesNotMatch=密码不正确
DirectoryNotExist=目录不存在
SpecifiedDirectoryShouldUnderSite=指定的目录应该位于站点目录下或者在安装目录外
InvalidDirectory=无效的目录
DnsName.message=应仅包含字母、数字或连字符(-)，并且只能以字母数字开头和结尾。
EvnName.message=名称应以字母开头，并且只能包含字母数字和下划线
Interpolative.message=最后的@是无效的。要么使用 @...@ 引用变量，要么使用 @@ 转译原来的 @
MalformedJobMatch=构建任务匹配格式错误
SpaceNotAllowed=不允许使用空格
MalformedNotificationReceiver=通知接收者格式错误
InvalidNumber=无效的数字
SlashAndBackSlashNotAllowed=不允许使用斜杠和反斜杠
NotMatchingRegularExpression=和指定的正则表达式不匹配
Option.message=选项
MalformedReviewRequirement=评审者格式错误
RoleName.message=角色名称不能包含'[', ']', '<', '>', '{', 和 '}'
AbsolutePathNotAllowed=这里不允许绝对路径
IsDisallowed.message=是禁止的
InvalidTagName=无效的git标签名称
MalformedUserMatch=用户匹配格式错误
TextInput.max.message=文本过长，最多500字符
TextInput.pattern.message=应匹配正则表达式:
AtLeastOneIterationNeedsToBeSelected=请至少选择一个迭代
NoPermissionToImportAsRootProjects=没有权限将它导入为一个根项目，请指定该项目的父项目
UrlMustBeginWithHttpOrHttps=正确的Url应以http或https开头
DuplicateWebhookUrl=重复的 webhook url
ShouldBeStartWithSEC=钉钉通知的加签密钥应以SEC开头
InvalidGpgPublicKey.message=无效的GPG公钥
InvalidCronExpression.message=cron表达式格式错误
AtLeastOneRoleIsRequired=请至少选择一个角色
MalformedRegularExpression=正则表达式格式错误
TheProvidedKeyIsNotValid=您提供的密钥无效，请检查后重试
NotAValidYouTrackApiUrl=该url不是有效的YouTrack api url
AuthenticationFailed=认证失败
