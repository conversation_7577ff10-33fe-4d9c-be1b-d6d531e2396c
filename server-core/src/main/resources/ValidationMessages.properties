AccessTokenEditBean.Authorizations.message=At least one project should be authorized
MayNotBeEmpty.message=May not be empty
MustNotBeEmpty.message=Must not be empty
GroovyScript.Name.message=Name is not allowed to start with 'builtin:'
Authenticator.UserSearchBases.message=At least one user search base should be specified
LdapAuthenticator.gr.message=may not be empty
MailService.timeout.message=This value should not be less than 5
InboxPollSetting.pollInterval.message=This value should not be less than 10
AtLeastOneEntryShouldBeSpecified=At least one entry should be specified
ChooseAColor=choose a color for this state
NeedTwoColums=At least two columns need to be defined
AtLeastOneStateNeedToSpecified=At least one state needs to be specified
AtLeastOneValueNeedToSpecified=At least one value needs to be specified
AtLeastOneChoiceNeedToSpecified=At least one choice need to be specified
ChooseRevision=Please choose revision to create branch from
ChooseRevisionForCreateTag=Please choose revision to create tag from
AtLeastOneRepoShoudBeSelected=At least one repository should be selected
AtLeastOneProjectShoudBeSelected=At least one project should be selected
JobSecret.name.message=Character '@' not allowed in secret name
JobProperty.name.message=Character '@' not allowed in property name
WebHook.eventType.message=At least one event type needs to be selected
ShouldNotBeLessThanOne=This value should not be less than 1
AtLeastOneOptionNeedsToBeSelected=At least one option needs to be selected
Project.serviceDescEmailAddress.message=character '~' not allowed in name part
Service.runAs.message=Should be specified in form of <uid>:<gid>
OnlyHttpOrHttpsProtocolIsSupported=Only http/https protocol is supported
Reserved.message=is reserved
StartAndEndWithAlphanumericOrNnderscore=Should start and end with alphanumeric or underscore. Only alphanumeric, underscore, dash, space and dot are allowed in the middle.
TimesheetEditBean.nameAlreadyUsed.message=Name already used by another timesheet in this project
TrivyScanStep.detectOption.message=At least one detect option should be specified
AtLeastOneStateShouldBeSpecified=At least one state should be specified
MalformedQuery.message=Malformed query
StartAndEndWithAlphanumericOrNnderscoreButNoSpaceInMiddle=Should start and end with alphanumeric or underscore. Only alphanumeric, underscore, dash, and dot are allowed in the middle.
ProjectPath.message=Should start and end with alphanumeric or underscore. Only slash, alphanumeric, underscore, dash, and dot are allowed in the middle.
InvalidGitBranchName=Invalid git branch name
StartWithAlphanumericOrNnderscore=Should start with alphanumeric or underscore, and contains only alphanumeric, underscore, dash, or dot
ShouldBeTwoOrMoreUppercaseLetters=Should be two or more uppercase letters
MalformedPatternSet.message=Malformed pattern set
MalformedGroovyTemplate.message=Malformed groovy template
FailedCompileGroovyScript.message=Failed to compile groovy script
NotAValidCommitHash=Not a valid commit hash
InvalidCronExpression=Invalid cron expression
CurrentPasswordDoesNotMatch=Current password does not match
DirectoryNotExist=Directory not exist
SpecifiedDirectoryShouldUnderSite=Specified directory should be either under site directory or outside of DevGrip installation directory
InvalidDirectory=Invalid directory
DnsName.message=Should only contain alphanumberic characters or '-', and can only start and end with alphanumeric characters
EvnName.message=name should start with letter and can only consist of alphanumeric and underscore characters
Interpolative.message=Invalid Last appearance of @. Either use @...@ to reference a variable, or use @@ for literal @
MalformedJobMatch=Malformed job match
SpaceNotAllowed=Space not allowed
MalformedNotificationReceiver=Malformed notification receiver
InvalidNumber=Invalid number
SlashAndBackSlashNotAllowed=Slash and back slash characters are not allowed
NotMatchingRegularExpression=Not matching specified regular expression
Option.message=Option
MalformedReviewRequirement=Malformed review requirement
RoleName.message=Role name can not container characters '[', ']', '<', '>', '{', and '}'
AbsolutePathNotAllowed=Absolute path not allowed
IsDisallowed.message=is disallowed
InvalidTagName=Invalid git tag name
MalformedUserMatch=Malformed user match
TextInput.max.message=Text is too long. Max 500 characters
TextInput.pattern.message=Should match regular expression: 
AtLeastOneIterationNeedsToBeSelected=At least one iteration needs to be selected
NoPermissionToImportAsRootProjects=No permission to import as root projects, please specify parent project
UrlMustBeginWithHttpOrHttps=Url beginning with http/https is expected
DuplicateWebhookUrl=Duplicate webhook url found
ShouldBeStartWithSEC=Dingtalk notification signing secret should be start with 'SEC'
InvalidGpgPublicKey.message=Invalid GPG Public key
InvalidCronExpression.message=Invalid cron expression
AtLeastOneRoleIsRequired=At least one role is required
MalformedRegularExpression=Malformed regular expression
TheProvidedKeyIsNotValid=The provided key is not valid. Please check and try again
NotAValidYouTrackApiUrl=This does not seem like a YouTrack api url
AuthenticationFailed=Authentication failed
