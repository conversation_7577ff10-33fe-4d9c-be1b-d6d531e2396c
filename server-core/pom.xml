<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<artifactId>server-core</artifactId>
	<parent>
		<groupId>net.devgrip</groupId>
		<artifactId>server</artifactId>
		<version>2025.3.7</version>
	</parent>
	<build>
		<plugins>
			<plugin>
				<groupId>org.antlr</groupId>
				<artifactId>antlr4-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>
	<dependencies>
		<dependency>
			<groupId>com.google.mug</groupId>
			<artifactId>mug</artifactId>
		</dependency>
		<dependency>
			<groupId>net.devgrip</groupId>
			<artifactId>commons-bootstrap</artifactId>
		</dependency>
		<dependency>
			<groupId>net.devgrip</groupId>
			<artifactId>commons-loader</artifactId>
		</dependency>
		<dependency>
			<groupId>net.devgrip</groupId>
			<artifactId>commons-loader</artifactId>
			<type>test-jar</type>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>net.devgrip</groupId>
			<artifactId>commons-codeassist</artifactId>
		</dependency>
		<dependency>
			<groupId>net.devgrip</groupId>
			<artifactId>commons-jsymbol</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-compress</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
		</dependency>
		<dependency>
			<groupId>org.jetbrains.xodus</groupId>
			<artifactId>xodus-entity-store</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.tika</groupId>
			<artifactId>tika-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.sun.mail</groupId>
			<artifactId>javax.mail</artifactId>
		</dependency>
		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
		</dependency>
		<dependency>
			<groupId>org.yaml</groupId>
			<artifactId>snakeyaml</artifactId>
		</dependency>
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk18on</artifactId>
		</dependency>
		<dependency>
			<groupId>org.unbescape</groupId>
			<artifactId>unbescape</artifactId>
		</dependency>
		<dependency>
			<groupId>org.quartz-scheduler</groupId>
			<artifactId>quartz</artifactId>
			<exclusions>
				<exclusion>
					<groupId>c3p0</groupId>
					<artifactId>c3p0</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.shiro</groupId>
			<artifactId>shiro-core</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.lucene</groupId>
			<artifactId>lucene-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.lucene</groupId>
			<artifactId>lucene-queries</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.lucene</groupId>
			<artifactId>lucene-queryparser</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.lucene</groupId>
			<artifactId>lucene-analyzers-common</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.lucene</groupId>
			<artifactId>lucene-backward-codecs</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.shiro</groupId>
			<artifactId>shiro-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.shiro</groupId>
			<artifactId>shiro-guice</artifactId>
			<exclusions>
				<exclusion>
					<groupId>javax.annotation</groupId>
					<artifactId>javax.annotation-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>javax.xml.bind</groupId>
			<artifactId>jaxb-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.glassfish</groupId>
			<artifactId>javax.el</artifactId>
		</dependency>
		<dependency>
			<groupId>org.hsqldb</groupId>
			<artifactId>hsqldb</artifactId>
		</dependency>
		<dependency>
			<groupId>jaxen</groupId>
			<artifactId>jaxen</artifactId>
		</dependency>
		<dependency>
			<groupId>com.thoughtworks.xstream</groupId>
			<artifactId>xstream</artifactId>
		</dependency>
		<dependency>
			<groupId>com.joestelmach</groupId>
			<artifactId>natty</artifactId>
		</dependency>
		<dependency>
			<groupId>com.nimbusds</groupId>
			<artifactId>oauth2-oidc-sdk</artifactId>
		</dependency>
		<dependency>
			<groupId>org.codehaus.groovy</groupId>
			<artifactId>groovy-templates</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.albfernandez</groupId>
			<artifactId>juniversalchardet</artifactId>
		</dependency>
		<dependency>
			<groupId>com.vladsch.flexmark</groupId>
			<artifactId>flexmark</artifactId>
		</dependency>
		<dependency>
			<groupId>com.vladsch.flexmark</groupId>
			<artifactId>flexmark-ext-autolink</artifactId>
		</dependency>
		<dependency>
			<groupId>com.vladsch.flexmark</groupId>
			<artifactId>flexmark-ext-anchorlink</artifactId>
		</dependency>
		<dependency>
			<groupId>com.vladsch.flexmark</groupId>
			<artifactId>flexmark-ext-definition</artifactId>
		</dependency>
		<dependency>
			<groupId>com.vladsch.flexmark</groupId>
			<artifactId>flexmark-ext-tables</artifactId>
		</dependency>
		<dependency>
			<groupId>com.vladsch.flexmark</groupId>
			<artifactId>flexmark-ext-gfm-tasklist</artifactId>
		</dependency>
		<dependency>
			<groupId>com.vladsch.flexmark</groupId>
			<artifactId>flexmark-ext-toc</artifactId>
		</dependency>
		<dependency>
			<groupId>com.vladsch.flexmark</groupId>
			<artifactId>flexmark-ext-gitlab</artifactId>
		</dependency>
		<dependency>
			<groupId>com.vladsch.flexmark</groupId>
			<artifactId>flexmark-ext-admonition</artifactId>
		</dependency>
		<dependency>
			<groupId>org.eclipse.jgit</groupId>
			<artifactId>org.eclipse.jgit.http.server</artifactId>
		</dependency>
		<dependency>
			<groupId>org.eclipse.jgit</groupId>
			<artifactId>org.eclipse.jgit.gpg.bc</artifactId>
		</dependency>
		<dependency>
			<groupId>org.eclipse.jgit</groupId>
			<artifactId>org.eclipse.jgit.archive</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.sshd</groupId>
			<artifactId>sshd-core</artifactId>
		</dependency>
		<dependency>
			<groupId>net.i2p.crypto</groupId>
			<artifactId>eddsa</artifactId>
		</dependency>
		<dependency>
			<groupId>org.eclipse.jetty</groupId>
			<artifactId>jetty-server</artifactId>
		</dependency>
		<dependency>
			<groupId>org.eclipse.jetty</groupId>
			<artifactId>jetty-servlets</artifactId>
		</dependency>
		<dependency>
			<groupId>org.eclipse.jetty</groupId>
			<artifactId>jetty-servlet</artifactId>
		</dependency>
		<dependency>
			<groupId>org.eclipse.jetty.websocket</groupId>
			<artifactId>websocket-server</artifactId>
		</dependency>
		<dependency>
			<groupId>org.glassfish.jersey.containers</groupId>
			<artifactId>jersey-container-servlet</artifactId>
		</dependency>
		<dependency>
			<groupId>org.glassfish.jersey.ext</groupId>
			<artifactId>jersey-bean-validation</artifactId>
		</dependency>
		<dependency>
			<groupId>org.glassfish.jersey.security</groupId>
			<artifactId>oauth2-client</artifactId>
		</dependency>
		<dependency>
			<groupId>org.hibernate.validator</groupId>
			<artifactId>hibernate-validator</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.wicket</groupId>
			<artifactId>wicket-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.wicket</groupId>
			<artifactId>wicket-extensions</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.wicket</groupId>
			<artifactId>wicket-request</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.wicket</groupId>
			<artifactId>wicket-util</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.wicket</groupId>
			<artifactId>wicket-devutils</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.wicket</groupId>
			<artifactId>wicket-native-websocket-core</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-validator</groupId>
			<artifactId>commons-validator</artifactId>
		</dependency>
		<dependency>
			<groupId>org.ocpsoft.prettytime</groupId>
			<artifactId>prettytime</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
		</dependency>
		<dependency>
			<groupId>org.glassfish.hk2</groupId>
			<artifactId>guice-bridge</artifactId>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-jcache</artifactId>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-entitymanager</artifactId>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-hikaricp</artifactId>
		</dependency>
		<dependency>
			<groupId>com.zaxxer</groupId>
			<artifactId>HikariCP</artifactId>
		</dependency>
		<dependency>
			<groupId>org.dom4j</groupId>
			<artifactId>dom4j</artifactId>
		</dependency>
		<dependency>
			<groupId>de.taimos</groupId>
			<artifactId>totp</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>javase</artifactId>
		</dependency>
		<dependency>
			<groupId>org.dhatim</groupId>
			<artifactId>fastexcel</artifactId>
		</dependency>
		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
		</dependency>
		<dependency>
			<groupId>net.devgrip</groupId>
			<artifactId>agent</artifactId>
		</dependency>
		<dependency>
			<groupId>org.objenesis</groupId>
			<artifactId>objenesis</artifactId>
		</dependency>
		<dependency>
			<groupId>org.connectbot</groupId>
			<artifactId>sshlib</artifactId>
			<version>2.2.23</version>
		</dependency>
		<dependency>
			<groupId>com.hazelcast</groupId>
			<artifactId>hazelcast</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.8.9</version>
		</dependency>
		<dependency>
			<groupId>com.hazelcast</groupId>
			<artifactId>hazelcast-hibernate53</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-csv</artifactId>
			<version>1.10.0</version>
		</dependency>
	</dependencies>
	<properties>
		<moduleClass>net.devgrip.server.CoreModule</moduleClass>
	</properties>
</project>
